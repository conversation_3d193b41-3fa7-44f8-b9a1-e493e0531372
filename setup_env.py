import os
import subprocess
import sys
import platform

def run_command(command):
    """运行命令并打印输出"""
    print(f"执行: {command}")
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        for line in process.stdout:
            print(line, end='')
        
        process.wait()
        return process.returncode
    except Exception as e:
        print(f"错误: {e}")
        return 1

def main():
    # 确定系统类型
    system = platform.system()
    print(f"检测到操作系统: {system}")
    
    # 设置虚拟环境名称
    venv_name = "venv"
    
    # 创建虚拟环境
    print("\n[1/3] 创建虚拟环境...")
    if os.path.exists(venv_name):
        print(f"虚拟环境 '{venv_name}' 已存在")
    else:
        if run_command(f"{sys.executable} -m venv {venv_name}") != 0:
            print("创建虚拟环境失败")
            return
    
    # 准备激活命令和pip路径
    if system == "Windows":
        activate_cmd = f"{venv_name}\\Scripts\\activate"
        python_cmd = f"{venv_name}\\Scripts\\python.exe"
    else:
        activate_cmd = f"source {venv_name}/bin/activate"
        python_cmd = f"{venv_name}/bin/python"
    
    # 升级pip
    print("\n[2/3] 升级pip...")
    if system == "Windows":
        if run_command(f"{python_cmd} -m pip install --upgrade pip") != 0:
            print("升级pip失败")
            return
    else:
        if run_command(f". {activate_cmd} && python -m pip install --upgrade pip") != 0:
            print("升级pip失败")
            return
    
    # 安装依赖
    print("\n[3/3] 安装requirements.txt中的依赖...")
    if not os.path.exists("requirements.txt"):
        print("requirements.txt文件不存在")
        return
    
    if system == "Windows":
        if run_command(f"{python_cmd} -m pip install -r requirements.txt") != 0:
            print("安装依赖失败")
            return
    else:
        if run_command(f". {activate_cmd} && python -m pip install -r requirements.txt") != 0:
            print("安装依赖失败")
            return
    
    # 完成
    print("\n设置完成!")
    print(f"虚拟环境已创建: {venv_name}")
    print("\n要激活虚拟环境，请运行:")
    if system == "Windows":
        print(f"{venv_name}\\Scripts\\activate")
    else:
        print(f"source {venv_name}/bin/activate")
    
    print("\n要运行应用，请激活虚拟环境后执行:")
    print("python app.py")

if __name__ == "__main__":
    main() 