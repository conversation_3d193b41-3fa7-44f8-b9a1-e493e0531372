<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>疾病监测与预测系统</title>
    <link rel="stylesheet" href="//unpkg.com/@arco-design/web-react@2.65.0/dist/css/arco.css">
    <link rel="stylesheet" href="//unpkg.com/@fortawesome/fontawesome-free@6.4.2/css/all.min.css">
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="//unpkg.com/react@18.2.0/umd/react.production.min.js"></script>
    <script src="//unpkg.com/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="//unpkg.com/@arco-design/web-react@2.65.0/dist/arco.min.js"></script>
    <style>
        :root {
            --primary-color: rgb(var(--primary-6));
            --border-radius: 8px;
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        body {
            background-color: var(--color-fill-2);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .menu-container {
            width: 98%;
            margin: 0 auto;
            border-radius: var(--border-radius);
            overflow: hidden;
            background: var(--color-bg-2);
            box-shadow: var(--card-shadow);
        }

        .menu-demo {
            padding: 8px 24px;
        }

        .hero-container {
            width: 98%;
            margin: 24px auto;
            border-radius: var(--border-radius);
            overflow: hidden;
            background: linear-gradient(135deg, #165DFF, #0FC6C2);
        }

        .hero-section {
            padding: 60px 0;
            text-align: center;
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 600;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: 1.2em;
            max-width: 800px;
            margin: 0 auto;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            line-height: 1.6;
        }

        .contestant-info {
            background: linear-gradient(135deg, #0a4cb5, #0aa5a2);
            padding: 20px 0;
            text-align: center;
            margin-bottom: 0;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .competition-details {
            margin-bottom: 15px;
            padding: 0 20px;
        }

        .competition-title {
            font-size: 1.3em;
            font-weight: 500;
            margin: 0 0 10px 0;
        }

        .competition-theme {
            font-size: 1.1em;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 15px;
        }

        .contestant-details {
            display: inline-flex;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50px;
            padding: 12px 25px;
            margin: 0 auto;
            align-items: center;
            backdrop-filter: blur(5px);
        }

        .contestant-details .info-item {
            margin: 0 20px;
            display: flex;
            align-items: center;
        }

        .contestant-details .info-item i {
            margin-right: 10px;
            font-size: 1.2em;
        }

        .features-container {
            width: 98%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .feature-card {
            background: var(--color-bg-2);
            border-radius: var(--border-radius);
            padding: 24px;
            box-shadow: var(--card-shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), #0FC6C2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-icon {
            font-size: 2.5em;
            color: var(--primary-color);
            margin-bottom: 16px;
            transition: transform 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1);
        }

        .feature-title {
            font-size: 1.5em;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--color-text-1);
        }

        .feature-description {
            color: var(--color-text-2);
            line-height: 1.6;
            margin-bottom: 20px;
            min-height: 80px;
        }

        .data-tag {
            display: inline-block;
            padding: 6px 16px;
            background: rgba(var(--primary-6), 0.1);
            color: var(--primary-color);
            border-radius: 20px;
            font-size: 0.9em;
            position: absolute;
            bottom: 20px;
            left: 24px;
            right: 24px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- 控制参赛者信息显示的开关 -->
    <script>
        // 控制参赛者信息显示的开关：true为显示，false为隐藏
        const showContestantInfo = true;
        
        document.addEventListener('DOMContentLoaded', function() {
            const contestantSection = document.querySelector('.contestant-info');
            const heroSection = document.querySelector('.hero-section');
            
            if (showContestantInfo) {
                contestantSection.style.display = 'block';
                heroSection.style.marginBottom = '0';
            } else {
                contestantSection.style.display = 'none';
                heroSection.style.marginBottom = '40px';
            }
        });
    </script>

    <!-- 导航菜单外层容器 -->
    <div class="menu-container">
        <div id="menu-container"></div>
    </div>

    <!-- 英雄区域和参赛信息的容器 -->
    <div class="hero-container">
        <!-- 英雄区域 -->
        <div class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">疾病监测与预测系统</h1>
                <p class="hero-subtitle">
                    基于大数据和人工智能的传染病监测、分析与预测平台<br>
                    让数据驱动决策，用AI守护健康
                </p>
            </div>
        </div>

        <!-- 参赛者信息区域 -->
        <div class="contestant-info">
            <div class="competition-details">
                <h3 class="competition-title">第五届长三角青少年人工智能奥林匹克挑战赛参赛作品</h3>
                <div class="competition-theme">
                    "数智创变者"赛道 | 主题：人工智能赋能数据多元表达 - 数据"会说话"
                </div>
            </div>
            <div class="contestant-details">
                <div class="info-item">
                    <i class="fas fa-school"></i>
                    <span class="school">XX中学</span>
                </div>
                <div class="info-item">
                    <i class="fas fa-users"></i>
                    <span class="class">X(X)班</span>
                </div>
                <div class="info-item">
                    <i class="fas fa-user"></i>
                    <span class="name">参赛者姓名</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能特性区域 -->
    <div class="features-container">
        <a href="/disease_prediction_china" class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="feature-title">传染病预测分析</h3>
            <p class="feature-description">
                融合SEIR模型和SARIMA时间序列分析，实现精准的疾病传播预测。AI驱动的专业分析报告，为防控决策提供数据支持。
            </p>
            <div class="data-tag">多维数据融合 · 趋势预测</div>
        </a>

        <a href="/cdc_news_china" class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-database"></i>
            </div>
            <h3 class="feature-title">疾控中心数据管理</h3>
            <p class="feature-description">
                智能采集和管理全国法定传染病疫情数据，支持多维度数据可视化展示，让数据更直观、更有价值。
            </p>
            <div class="data-tag">数据采集 · 可视化呈现</div>
        </a>

        <a href="/llm_config" class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-robot"></i>
            </div>
            <h3 class="feature-title">AI模型配置管理</h3>
            <p class="feature-description">
                灵活配置大语言模型接口参数，优化模型性能，提供更准确的疾病分析报告和预测结果。
            </p>
            <div class="data-tag">AI赋能 · 智能分析</div>
        </a>
    </div>

    <script>
        // 渲染顶部菜单
        function renderTopMenu() {
            const { Menu } = arco;
            const MenuItem = Menu.Item;

            const menuComponent = React.createElement(
                'div',
                { className: 'menu-demo' },
                React.createElement(
                    Menu,
                    { mode: 'horizontal', defaultSelectedKeys: ['1'] },
                    React.createElement(
                        MenuItem,
                        {
                            key: '0',
                            style: { padding: 0, marginRight: 8 },
                            disabled: true
                        }
                    ),
                    React.createElement(MenuItem, { key: '1', onClick: () => window.location.href = '/'}, '首页'),
                    React.createElement(MenuItem, { key: '2', onClick: () => window.location.href = '/disease_prediction_china' }, '传染病预测'),
                    React.createElement(MenuItem, { key: '3', onClick: () => window.location.href = '/cdc_news_china' }, '疾控中心数据')
                )
            );

            ReactDOM.createRoot(document.getElementById('menu-container')).render(menuComponent);
        }

        // 初始化顶部菜单
        renderTopMenu();
    </script>
</body>
</html> 