#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from routes.predict_disease_china import DiseasePredictionChina
from datetime import datetime
import traceback

def debug_prediction():
    """调试预测功能"""
    print("=== 调试预测功能 ===")
    
    try:
        predictor = DiseasePredictionChina()
        
        # 测试参数
        disease = '病毒性肝炎'
        start_date = datetime(2022, 1, 1)
        end_date = datetime(2023, 12, 31)
        prediction_months = 6
        
        print(f"测试疾病: {disease}")
        print(f"时间范围: {start_date} 到 {end_date}")
        print(f"预测月数: {prediction_months}")
        
        # 步骤1: 测试数据获取
        print("\n步骤1: 测试数据获取...")
        df = predictor.get_data_from_db(start_date, end_date)
        print(f"获取到数据行数: {len(df)}")
        
        if len(df) > 0:
            print(f"数据列: {df.columns.tolist()}")
            unique_diseases = df['disaster'].unique()
            print(f"数据库中的疾病数量: {len(unique_diseases)}")
            print(f"是否包含测试疾病: {disease in unique_diseases}")
            
            if disease in unique_diseases:
                disease_df = df[df['disaster'] == disease]
                print(f"该疾病的数据行数: {len(disease_df)}")
            else:
                print(f"可用的疾病: {unique_diseases[:10]}")  # 显示前10个
                return
        
        # 步骤2: 测试数据准备
        print("\n步骤2: 测试数据准备...")
        disease_data = predictor.prepare_data(df, disease)
        
        if disease_data is not None:
            print(f"准备的数据行数: {len(disease_data)}")
            print(f"数据列: {disease_data.columns.tolist()}")
        else:
            print("数据准备失败")
            return
        
        # 步骤3: 测试SEIR预测
        print("\n步骤3: 测试SEIR预测...")
        try:
            seir_result, seir_status = predictor.get_prediction_for_china(
                disease, start_date, end_date, prediction_months
            )
            print(f"SEIR预测状态: {seir_status}")
            if seir_status == "success":
                print(f"SEIR预测数据点数: {len(seir_result['dates'])}")
            else:
                print(f"SEIR预测失败: {seir_status}")
        except Exception as e:
            print(f"SEIR预测异常: {e}")
            traceback.print_exc()
        
        # 步骤4: 测试SARIMA预测
        print("\n步骤4: 测试SARIMA预测...")
        try:
            from routes.sarima_prediction_china import SARIMAPredictionChina
            sarima_predictor = SARIMAPredictionChina()
            sarima_result, sarima_status = sarima_predictor.get_prediction(
                disease, start_date, end_date, prediction_months
            )
            print(f"SARIMA预测状态: {sarima_status}")
            if sarima_status == "success":
                print(f"SARIMA预测数据点数: {len(sarima_result['dates'])}")
            else:
                print(f"SARIMA预测失败: {sarima_status}")
        except Exception as e:
            print(f"SARIMA预测异常: {e}")
            traceback.print_exc()
        
        # 步骤5: 测试组合预测
        print("\n步骤5: 测试组合预测...")
        try:
            combined_result, combined_status = predictor.get_combined_prediction(
                disease, start_date, end_date, prediction_months
            )
            print(f"组合预测状态: {combined_status}")
            if combined_status == "success":
                print(f"组合预测数据点数: {len(combined_result['dates'])}")
                print("✅ 组合预测成功")
            else:
                print(f"组合预测失败: {combined_status}")
        except Exception as e:
            print(f"组合预测异常: {e}")
            traceback.print_exc()
        
    except Exception as e:
        print(f"调试失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    debug_prediction()
