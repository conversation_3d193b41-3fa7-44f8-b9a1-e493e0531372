#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from datetime import datetime


def test_prediction_api():
    """测试预测API的完整流程"""
    print("=== 预测API测试 ===")

    # 测试数据
    test_data = {
        "disease": "病毒性肝炎",  # 使用数据库中存在的疾病
        "start_date": "2022-01-01",
        "end_date": "2023-12-31",
        "prediction_period": 6,
    }

    # API端点
    url = "http://localhost:83/predict_disease_china"

    try:
        print(f"发送请求到: {url}")
        print(f"请求数据: {test_data}")

        # 发送POST请求
        response = requests.post(url, data=test_data, timeout=30)

        print(f"响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()

            # 验证返回数据结构
            required_fields = [
                "historical_dates",
                "dates",
                "historical_data",
                "predictions",
            ]
            missing_fields = [field for field in required_fields if field not in result]

            if missing_fields:
                print(f"❌ 缺少必要字段: {missing_fields}")
                return False

            print("✅ 数据结构验证通过")

            # 验证数据内容
            print(f"历史数据点数: {len(result['historical_dates'])}")
            print(f"预测数据点数: {len(result['dates'])}")

            # 验证预测数据结构
            predictions = result["predictions"]
            if "growth" in predictions and "mean" in predictions["growth"]:
                print(f"预测增长数据点数: {len(predictions['growth']['mean'])}")
                print(f"预测增长样本值: {predictions['growth']['mean'][:3]}")
                print("✅ 预测数据格式正确")
            else:
                print("❌ 预测数据格式错误")
                return False

            # 检查是否包含SEIR和SARIMA原始结果
            if "seir" in result and "sarima" in result:
                print("✅ 包含SEIR和SARIMA原始结果")
            else:
                print("❌ 缺少SEIR或SARIMA原始结果")

            return True

        else:
            print(f"❌ 请求失败: {response.status_code}")
            try:
                error_info = response.json()
                print(f"错误信息: {error_info}")
            except:
                print(f"响应内容: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_diseases_api():
    """测试获取疾病列表API"""
    print("\n=== 疾病列表API测试 ===")

    url = "http://localhost:83/get_diseases_china"

    try:
        response = requests.get(url, timeout=10)
        print(f"响应状态码: {response.status_code}")

        if response.status_code == 200:
            diseases = response.json()
            print(f"✅ 获取到 {len(diseases)} 种疾病")
            if diseases:
                # 安全地显示前5个疾病
                sample_diseases = diseases[:5] if len(diseases) >= 5 else diseases
                print(f"疾病样本: {sample_diseases}")
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def start_server_if_needed():
    """检查服务器是否运行，如果没有则提示启动"""
    try:
        response = requests.get("http://localhost:83/", timeout=5)
        print("✅ 服务器已运行")
        return True
    except:
        print("❌ 服务器未运行")
        print("请先启动服务器:")
        print("1. 双击 start.bat")
        print("2. 或运行: python app.py")
        return False


if __name__ == "__main__":
    print("开始API测试...")

    if not start_server_if_needed():
        exit(1)

    # 测试疾病列表API
    diseases_ok = test_diseases_api()

    if diseases_ok:
        # 测试预测API
        prediction_ok = test_prediction_api()

        if prediction_ok:
            print("\n🎉 所有测试通过！修复成功！")
            print("\n修复验证结果:")
            print("✅ 组合预测功能已启用")
            print("✅ 数据库查询功能正常")
            print("✅ 前端数据格式兼容")
            print("✅ API响应结构正确")
        else:
            print("\n❌ 预测API测试失败")
    else:
        print("\n❌ 疾病列表API测试失败")
