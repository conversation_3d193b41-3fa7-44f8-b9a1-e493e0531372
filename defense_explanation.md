# 疾病预测模型演示说明（面向评委）

尊敬的各位评委老师，大家好！

在我们的"疾病预测分析系统"中，最核心的功能之一就是对疾病的未来趋势进行预测。为了实现这个功能，我们主要学习和应用了两种预测模型：SEIR 模型和 SARIMA 模型。接下来，我将用我自己的理解，向大家解释一下这两种模型大致是怎么工作的。

## 一、SEIR 模型：模拟疾病如何"传染"

SEIR 模型听起来可能有点复杂，但它的想法其实很像我们在模拟真实世界里传染病是怎么传播开来的。它把一个区域里的人分成了四种状态：

1.  **S (Susceptible - 易感者):** 就是那些身体健康，但还没有对这种病免疫，所以有可能会被感染的人。可以想象成人群中"等待"被感染的那些人。
2.  **E (Exposed - 潜伏者):** 这部分人比较特殊，他们其实已经被病毒感染了，但是病毒还在他们身体里"潜伏"，他们自己可能还没感觉到，或者暂时还不会把病毒传给别人。就像病毒的"潜伏期"。
3.  **I (Infected - 感染者):** 这就是我们通常说的"病人"了。他们身体里的病毒已经开始活跃，他们会表现出症状，并且能够把病毒传染给那些易感的健康人。
4.  **R (Recovered/Removed - 康复者/移除者):** 这部分人要么是通过治疗或者自身免疫战胜了病毒，身体康复了，并且在一段时间内不会再得这种病了；要么很不幸，因为这种疾病去世了。在模型里，这两类人都会从感染者队伍中"移除"。

**SEIR 模型是怎么工作的呢？**

它就像一个动态的"人群转换"游戏：

*   一开始，大部分人都是 S (易感者)。
*   当有 I (感染者) 出现时，他们会接触 S (易感者)，S 就有一定几率变成 E (潜伏者)。这个"几率"在模型里就和一个叫 **`beta` (传染率)** 的参数有关系。`beta` 越大，S 变成 E 的速度就越快，说明病更容易传开。
*   E (潜伏者) 过一段时间后（这个时间的长短和另一个叫 **`sigma` (潜伏期转化率)** 的参数有关，它是潜伏期天数的倒数），就会变成 I (感染者)，开始能够传染别人了。
*   I (感染者) 也会经过一段时间（这个时间和 **`gamma` (恢复率)** 参数有关，它是感染持续天数的倒数），最终变成 R (康复者/移除者)。

我们项目里的 SEIR 模型，就是通过设置这些参数（比如 `beta`、`sigma`、`gamma` 和总人口 `N`），再告诉模型一开始这四种状态的人各有多少（比如 `S0`, `E0`, `I0`, `R0`），然后计算机就能帮我们算出未来每一天，这四种状态的人数会怎么变化。这样，我们就能看到疾病传播的趋势，比如什么时候感染人数会达到高峰。

**在我们的系统里：**

*   我们会根据用户选择的不同疾病（比如流感和新冠），预设一些不同的潜伏期和感染持续时间，这样 `sigma` 和 `gamma` 就会不一样。
*   我们还尝试让 `beta` (传染率) 根据最近一段时间的历史数据动态调整。如果数据显示最近增长很快，那 `beta` 就会大一些。
*   人口总数 `N` 我们目前是固定参考中国的人口来设置的。

虽然模型背后的数学公式（微分方程）我们初中同学可能还不太好完全搞懂，但是通过调用 Python 里的科学计算库 `scipy`，我们就可以让计算机帮我们完成这些复杂的计算。我们主要做的就是理解模型的思想，准备好数据，调整参数，然后解读计算机给出的预测结果。

## 二、SARIMA 模型：从历史数据中找规律"算命"

SARIMA 模型和 SEIR 的思路不太一样。SEIR 更像是模拟真实过程，而 SARIMA 更像一个厉害的"算命先生"，它专门分析**过去的数据**，从中找出规律，然后用这些规律来预测**未来的数据**。它特别适合分析那些有明显"一年一个轮回"或者"一段时间一个周期"特点的数据，比如流感，每年冬天和春天就容易高发，这就是一种**季节性 (Seasonality)**。

**SARIMA 模型关注什么呢？**

1.  **历史数据本身 (AR - Autoregressive 自回归):** 它觉得，一个东西今天的数据，可能跟它昨天、前天的数据有关系。比如，如果昨天病例多，今天可能也少不了。
2.  **历史数据的"误差" (MA - Moving Average 移动平均):** 有时候预测会不准，它也会分析这些不准的"误差"本身有没有什么规律。
3.  **数据的平稳性 (I - Integrated 差分):** 为了让数据更容易分析，它有时候会做一些"差分"处理，比如用今天的数据减去昨天的数据，让数据的趋势更明显。
4.  **季节性 (S - Seasonal):** 这是 SARIMA 的"S"的来源。它会特别关注数据是不是有固定的周期性变化，比如上面说的流感每年冬春高发。对于月度数据，这个周期通常就是12个月。

**SARIMA 模型在我们的项目中是怎么用的呢？**

1.  **数据准备是关键：**
    *   我们首先把每天的疾病数据按月份汇总，得到每个月的新增病例数。
    *   **考虑到假期的影响：** 我们发现，像春节、国庆节这样的大假期，大家出门多、聚会多，可能会影响疾病传播。所以我们设置了一个 **`holiday_weights` (假期权重)**，比如春节那个月，我们会把病例数稍微乘上一个大一点的权重，国庆节也类似。
    *   **考虑到疾病本身的季节特点：** 不同的病在不同季节的活跃程度不一样。比如流感冬天厉害，手足口病可能春夏厉害。我们为每种疾病定义了详细的特征，包括：
        - `incubation_period`（潜伏期）：比如流感是2天，新冠是5天
        - `r0`（基本再生数）：表示一个感染者平均能传染给多少人
        - `seasonal_pattern`（季节性模式）：1-12月的系数，反映疾病在不同月份的活跃程度
        - `transmission_type`（传播类型）：如呼吸道传播、接触传播等
    *   这些疾病特征信息帮助我们更好地理解每种疾病的传播特性，并在预测时考虑这些因素。

2.  **模型参数：**
    *   我们使用了一个相对简单但有效的参数组合：`(1,1,1)` 和季节性参数 `(1,1,1,12)`
    *   这个组合在大多数情况下都能给出不错的预测结果，而且计算速度较快

3.  **预测和结果调整：**
    *   模型会根据历史数据预测未来几个月的病例数
    *   我们会根据疾病特征和假期权重调整预测值
    *   同时计算预测的置信区间，帮助用户了解预测的不确定性

**我们学习 SARIMA 的体会：**

SARIMA 模型对我们来说挑战更大一些，因为它涉及的统计概念更多。我们主要通过阅读资料，理解了它"从历史找规律，特别是季节性规律"的核心思想。我们系统里，最重要的是数据预处理那部分，也就是怎么把假期、季节因素考虑到数据里去。模型的具体拟合和计算，我们是依赖 Python 的 `statsmodels` 库来完成的。

## 三、组合预测：取长补短

在我们的系统中，我们不仅单独使用SEIR和SARIMA模型，还尝试将它们的预测结果组合起来，以获得更准确的预测。具体来说：

1. **权重分配**：
   - SEIR模型权重：0.4
   - SARIMA模型权重：0.6
   
   这个权重分配是基于两个模型的特点：
   - SARIMA模型更依赖历史数据，在数据充分的情况下表现更稳定
   - SEIR模型能更好地捕捉疾病传播的动力学特征，但参数估计可能不够精确

2. **组合方式**：
   - 对每个预测时间点，我们分别获取两个模型的预测值
   - 使用上述权重进行加权平均
   - 同时合并两个模型的置信区间，得到更全面的不确定性估计

3. **结果展示**：
   - 我们不仅展示组合后的预测结果
   - 还保留了原始的两个模型的预测值
   - 这样用户可以根据需要选择使用哪个预测结果

这种组合预测的方法，让我们能够同时利用两个模型的优势，提高预测的准确性和可靠性。

## 三、我们用到的主要架构组件（技术简介）

在搭建这个系统的过程中，我们像搭积木一样，用到了很多现成的工具和技术，它们帮助我们更快更好地实现了想法。这里简单介绍几个主要的：

*   **Flask (后端框架):** 
    *   **它是什么？** Flask 是一个用 Python 语言写的轻量级 Web 应用框架。可以把它想象成我们网站的"骨架"或者"后台总管"。它负责处理用户从浏览器发过来的请求（比如点击一个按钮），然后调用我们写的 Python 代码（比如执行预测模型），最后把结果返回给浏览器显示出来。
    *   **为什么选它？** 因为 Flask 比较简洁，学习起来相对容易上手，对于我们初中学生来说，可以更快地搭建起一个网站的后端服务，而不用一开始就陷入太复杂的配置。它的灵活性也比较高，需要什么功能可以自己添加。

*   **SQLite (数据库):**
    *   **它是什么？** SQLite 是一个轻型的、文件型的数据库。我们用它来存储和管理项目中的数据，比如收集到的历史疾病数据、用户的配置信息等。它不像一些大型数据库需要单独的服务器，SQLite 的数据库就是一个单独的文件，放在我们项目里，非常方便。
    *   **为什么选它？** 对于我们这个规模的项目来说，SQLite 足够用了，而且它配置简单，不需要复杂的安装和管理，让我们能更专注于数据本身和程序逻辑。Python 也内置了对 SQLite 的支持，用起来很方便。

*   **Arco Design (前端UI组件库):**
    *   **它是什么？** Arco Design 是一套由字节跳动团队开源的企业级产品设计系统，里面包含了很多设计好的、可以直接拿来用的网页界面元素，比如按钮、表单、导航栏、提示框等等。这些元素都符合一套美观、专业的设计规范。
    *   **为什么选它？** 作为初中生，我们可能不太擅长专业的前端页面设计。Arco Design 提供了很多现成的、漂亮的"积木块"，我们可以直接用它们来搭建用户界面，让我们的网站看起来更专业、更好用，而不用自己从头画每一个按钮。它能大大节省我们开发界面的时间。

*   **AG-Grid (表格组件):**
    *   **它是什么？** AG-Grid 是一个功能非常强大的 JavaScript 表格组件。当我们需要在网页上显示大量数据，并且希望用户能方便地对这些数据进行排序、筛选、分组等操作时，AG-Grid 就非常有用了。
    *   **为什么选它？** 我们的系统需要展示不少从数据库里读出来的疾病数据，如果用普通的 HTML 表格，功能会很有限。AG-Grid 让我们能以一种更专业、更灵活的方式展示这些数据，用户体验会更好。

*   **ECharts (图表组件):**
    *   **它是什么？** ECharts 是一个非常流行的开源可视化图表库。它能帮助我们把枯燥的数据变成各种直观、动态、可交互的图表，比如折线图、柱状图、饼图等。
    *   **为什么选它？** 疾病预测的结果，用图表来展示是最清晰明了的。ECharts 功能强大，图表类型丰富，而且可以做出很好看的交互效果，能让用户更好地理解数据和预测趋势。

选择这些工具，主要是因为它们对学生开发者比较友好，社区活跃，文档也比较完善，能让我们在有限的时间内，更专注于实现项目的核心功能——也就是疾病预测和分析，而不是在基础的架构和界面上花费过多精力。

## 总结

SEIR 模型帮助我们从"机理"上理解疾病传播，而 SARIMA 模型则帮助我们从"数据"中挖掘趋势。它们各有侧重，也各有优缺点。我们希望通过这两个模型，以及我们选择的这些技术工具，能为疾病的预防和控制提供一些有用的参考信息。

在向大家展示这些模型的同时，我们也想分享一下我们项目中使用的一些工具和方法。比如，大家看到的这些图表和表格，我们是使用了一些很棒的开源组件，像 ECharts、Arco Design 和 AG-Grid 来实现的，它们让数据展示更直观。**另外，在搭建整个网站应用的过程中，我们就像多了一个聪明的助手一样，经常向AI大模型提问，它帮助我们学习和解决了不少搭建过程中的难题。**

在开发这个功能的过程中，我们遇到了很多挑战，也学到了很多新知识。虽然我们对模型的理解可能还不够深入，但我们努力去学习和应用，希望各位评委老师能看到我们的努力和思考。

谢谢大家！ 