<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>大模型配置管理</title>
    <link rel="stylesheet" href="//unpkg.com/@arco-design/web-react@2.65.0/dist/css/arco.css">
    <link rel="stylesheet" href="//unpkg.com/@fortawesome/fontawesome-free@6.4.2/css/all.min.css">
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="//unpkg.com/react@18.2.0/umd/react.production.min.js"></script>
    <script src="//unpkg.com/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="//unpkg.com/@arco-design/web-react@2.65.0/dist/arco.min.js"></script>
    <style>
        body {
            background-color: var(--color-fill-2);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        .config-container {
            width: 98%;
            max-width: 800px;
            margin: 20px auto;
            padding: 24px;
            background: var(--color-bg-2);
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        .form-item {
            margin-bottom: 24px;
        }
    </style>
</head>
<body>
    <!-- 导航菜单容器 -->
    <div id="menu-container"></div>
    
    <!-- 配置表单容器 -->
    <div id="config-container"></div>

    <script>
        // 渲染顶部菜单
        function renderTopMenu() {
            const { Menu } = arco;
            const MenuItem = Menu.Item;

            const menuComponent = React.createElement(
                'div',
                { className: 'menu-demo' },
                React.createElement(
                    Menu,
                    { mode: 'horizontal', defaultSelectedKeys: ['4'] },
                    React.createElement(MenuItem, { key: '1', onClick: () => window.location.href = '/' }, '首页'),
                    React.createElement(MenuItem, { key: '2', onClick: () => window.location.href = '/disease_prediction_china' }, '传染病预测'),
                    React.createElement(MenuItem, { key: '3', onClick: () => window.location.href = '/cdc_news_china' }, '疾控中心数据'),
                    React.createElement(MenuItem, { key: '4' }, '大模型配置')
                )
            );

            ReactDOM.createRoot(document.getElementById('menu-container')).render(menuComponent);
        }

        // 渲染配置表单
        function renderConfigForm() {
            const { Form, Input, Select, Button, Message } = arco;
            const FormItem = Form.Item;
            const TextArea = Input.TextArea;

            class ConfigForm extends React.Component {
                formRef = React.createRef();

                componentDidMount() {
                    // 加载配置
                    fetch('/api/llm_config/load')
                        .then(response => response.json())
                        .then(data => {
                            this.formRef.current.setFieldsValue(data);
                        })
                        .catch(error => {
                            console.error('加载配置失败:', error);
                            Message.error('加载配置失败');
                        });
                }

                handleSubmit = () => {
                    this.formRef.current.validate().then(values => {
                        fetch('/api/llm_config/save', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(values)
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                Message.success('配置保存成功');
                            } else {
                                Message.error('配置保存失败');
                            }
                        })
                        .catch(error => {
                            console.error('保存配置失败:', error);
                            Message.error('配置保存失败');
                        });
                    });
                };

                render() {
                    return React.createElement(
                        'div',
                        { className: 'config-container' },
                        React.createElement(
                            Form,
                            {
                                ref: this.formRef,
                                autoComplete: 'off',
                                style: { width: '100%' },
                                labelCol: { span: 4 },
                                wrapperCol: { span: 20 }
                            },
                            React.createElement(
                                FormItem,
                                {
                                    label: '接口地址',
                                    field: 'apiEndpoint',
                                    rules: [{ required: true, message: '请输入接口地址' }]
                                },
                                React.createElement(Input, { placeholder: '请输入接口地址' })
                            ),
                            React.createElement(
                                FormItem,
                                {
                                    label: 'API Key',
                                    field: 'apiKey',
                                    rules: [{ required: true, message: '请输入API Key' }]
                                },
                                React.createElement(Input.Password, { placeholder: '请输入API Key' })
                            ),
                            React.createElement(
                                FormItem,
                                {
                                    label: 'AI模型',
                                    field: 'model',
                                    rules: [{ required: true, message: '请选择AI模型' }]
                                },
                                React.createElement(
                                    Select,
                                    { placeholder: '请选择AI模型' },
                                    React.createElement(Select.Option, { value: 'free:Qwen3-30B-A3B' }, 'Qwen3-30B-A3B'),
                                    React.createElement(Select.Option, { value: 'free:QwQ-32B' }, 'QwQ-32B')
                                )
                            ),
                            React.createElement(
                                FormItem,
                                {
                                    label: '提示词',
                                    field: 'prompt',
                                    rules: [{ required: true, message: '请输入提示词' }]
                                },
                                React.createElement(TextArea, {
                                    placeholder: '请输入提示词',
                                    style: { minHeight: 200, fontFamily: 'monospace' }
                                })
                            ),
                            React.createElement(
                                FormItem,
                                { wrapperCol: { offset: 4 } },
                                React.createElement(
                                    Button,
                                    {
                                        type: 'primary',
                                        onClick: this.handleSubmit
                                    },
                                    '保存配置'
                                )
                            )
                        )
                    );
                }
            }

            ReactDOM.createRoot(document.getElementById('config-container')).render(
                React.createElement(ConfigForm)
            );
        }

        // 初始化页面
        renderTopMenu();
        renderConfigForm();
    </script>
</body>
</html> 