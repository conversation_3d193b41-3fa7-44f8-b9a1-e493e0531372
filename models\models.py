from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import sqlite3

db = SQLAlchemy()

class Disease(db.Model):
    __tablename__ = 'news_diseases'
    data_value = db.Column(db.String(255), primary_key=True)
    name = db.Column(db.String(255), nullable=False)

class DisasterChina(db.Model):
    """中国疾控中心疾病数据模型"""
    __tablename__ = 't_disaster_china'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    time = db.Column(db.DateTime)
    disaster = db.Column(db.String(255))
    disaster_type = db.Column(db.String(255))
    sub_disaster = db.Column(db.String(255))
    cities = db.Column(db.String(255))
    source_url = db.Column(db.String(1000))
    growth = db.Column(db.Integer, nullable=False)
    deaths = db.Column(db.Integer)
    cures = db.Column(db.Integer)
    sub_growth = db.Column(db.Integer, nullable=False)
    sub_deaths = db.Column(db.Integer)
    sub_cures = db.Column(db.Integer)
    update_time = db.Column(db.DateTime)
    
    # SQLite不支持comment参数，所以移除了所有comment
