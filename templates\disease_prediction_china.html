<!DOCTYPE html>
<html>
<head>
    <title>中国传染病预测分析</title>
    <link rel="stylesheet" href="//unpkg.com/@arco-design/web-react@2.65.0/dist/css/arco.css">
    <link rel="stylesheet" href="//unpkg.com/@fortawesome/fontawesome-free@6.4.2/css/all.min.css">
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="//unpkg.com/react@18.2.0/umd/react.production.min.js"></script>
    <script src="//unpkg.com/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="//unpkg.com/@arco-design/web-react@2.65.0/dist/arco.min.js"></script>
    <script src="{{ url_for('static', filename='js/moment.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/echarts.min.js') }}"></script>
    <style>
        :root {
            --primary-color: rgb(var(--primary-6));
            --border-radius: 8px;
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        body {
            background-color: var(--color-fill-2);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .menu-demo {
            width: 98%;
            margin: 0 auto 24px auto;
            background: var(--color-bg-2);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 8px 24px;
        }



        .container {
            width: 98%;
            margin: 0 auto;
        }

        .page-header {
            margin-bottom: 24px;
            padding: 16px 24px;
            background: linear-gradient(to right, var(--primary-color), #4080ff);
            border-radius: var(--border-radius);
            color: white;
            box-shadow: var(--card-shadow);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-title i {
            font-size: 28px;
        }

        .content-card {
            background: var(--color-bg-2);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            overflow: hidden;
            width: 100%;
        }

        .controls {
            margin-bottom: 24px;
            padding: 24px;
            background: var(--color-bg-2);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
        }

        .controls-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--color-text-1);
            margin: 0 0 20px 0;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--color-border);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .controls-title i {
            font-size: 24px;
            color: var(--primary-color);
        }

        .filter-wrapper {
            display: flex;
            gap: 24px;
            align-items: center;
            padding: 16px 0;
        }

        .disease-date-wrapper {
            display: flex;
            align-items: center;
            gap: 16px;
            background: var(--color-bg-2);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            padding: 8px 16px;
            height: 40px;
            width: 100%;
        }

        .filter-item {
            display: flex;
            align-items: center;
            width: auto;
            min-width: 180px;
        }

        .date-range-wrapper {
            width: auto;
            min-width: 280px;
        }

        .prediction-period {
            display: flex;
            align-items: center;
            gap: 16px;
            width: auto;
            min-width: 160px;
        }

        .chart-wrapper {
            background: var(--color-bg-2);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            padding: 5px;
            margin-top: 20px;
            position: relative;
        }

        .chart-wrapper .help-icon {
            position: absolute;
            right: 10px;
            top: 10px;
            z-index: 1;
        }

        .chart-wrapper .help-icon i {
            font-size: 20px;
            color: #1d2129;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .chart-wrapper .help-icon i:hover {
            opacity: 1;
        }

        .chart-container {
            padding: 0;
            height: 600px;
            width: 100%;
        }

        .generate-button {
            display: flex;
            align-items: center;
            width: auto;
        }

        #dateRangePicker .arco-picker {
            width: 100% !important;
        }

        .prediction-period .arco-slider {
            width: 160px;
        }

        /* 优化滑块样式 */
        .arco-slider {
            width: 160px !important;
            margin: 0;
            padding: 0;
        }

        .arco-slider-wrapper {
            margin: 0;
        }

        .arco-slider-marks-text {
            font-size: 12px;
            color: var(--color-text-3);
        }

        /* 全屏加载动画样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(255, 255, 255, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-overlay.active {
            display: flex;
        }

        .loading-content {
            text-align: center;
        }

        .loading-text {
            margin-top: 16px;
            color: var(--color-text-1);
            font-size: 16px;
        }

        .think-container {
            margin: 10px 0;
            border: 1px solid var(--color-border);
            border-radius: 4px;
            overflow: hidden;
        }
        .think-header {
            background-color: var(--color-fill-2);
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            user-select: none;
        }
        .think-header:hover {
            background-color: var(--color-fill-3);
        }
        .think-content {
            padding: 12px;
            background-color: var(--color-fill-1);
            white-space: pre-wrap;
            display: none;
        }
        .think-content.expanded {
            display: block;
        }
        .think-toggle {
            color: var(--color-text-3);
            font-size: 12px;
        }
        .non-think-content {
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>


    <!-- 添加加载动画覆盖层 -->
    <div class="loading-overlay">
        <div class="loading-content">
            <div id="loadingSpinner"></div>
            <div class="loading-text">正在生成中...</div>
        </div>
    </div>

    <div class="container">
        <main class="content-card">
                <!-- 水平菜单 -->
    <div id="menu-container"></div>
            <div class="controls">
                <h2 class="controls-title">
                    <i class="arco-icon-line-chart"></i>
                    中国传染病预测分析
                </h2>
                <div class="filter-wrapper">
                    <div class="disease-date-wrapper">
                        <div class="filter-item" id="diseaseSelect"></div>
                        <div class="date-range-wrapper" id="dateRangePicker"></div>
                        <div class="prediction-period">
                            <div id="predictionInput"></div>
                        </div>
                        <div class="filter-item" id="indexSelect"></div>
                        <div class="generate-button" id="generateButton"></div>
                    </div>
                </div>
            </div>
            <div class="chart-wrapper">
                <div class="help-icon">
                    <i class="fa-regular fa-circle-question" title="图表说明：实线表示历史数据，虚线表示预测数据，浅色区域表示预测的95%置信区间"></i>
                </div>
                <div class="chart-container"></div>
            </div>
        </main>
    </div>

    <script>
        const { Select, DatePicker, InputNumber, Button, Message, Tag, Tooltip, Drawer, Spin, Modal } = arco;
        
        // 全局变量用于存储选中的指标
        window.selectedIndices = ['growth', 'deaths'];
        
        // 添加全局变量
        let chartData = null;
        let selectedDisease = null;
        let startDate = null;
        let endDate = null;
        let fullReportContent = ''; // 添加到全局，用于存储完整报告内容
        
        // 渲染帮助抽屉
        function renderHelpDrawer() {
            const DrawerContent = () => {
                const style = {
                    section: {
                        marginBottom: '24px'
                    },
                    title: {
                        fontSize: '16px',
                        fontWeight: 'bold',
                        marginBottom: '12px',
                        color: 'var(--color-text-1)'
                    },
                    text: {
                        fontSize: '14px',
                        lineHeight: '1.6',
                        color: 'var(--color-text-2)',
                        marginBottom: '8px'
                    },
                    formula: {
                        backgroundColor: 'var(--color-fill-2)',
                        padding: '12px',
                        borderRadius: '4px',
                        fontFamily: 'monospace',
                        marginBottom: '12px'
                    },
                    list: {
                        paddingLeft: '20px',
                        marginBottom: '12px'
                    },
                    listItem: {
                        marginBottom: '8px',
                        color: 'var(--color-text-2)'
                    }
                };

                return React.createElement('div', null, [
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '预测方法概述'),
                        React.createElement('div', { style: style.text }, 
                            '本模块使用SARIMA（季节性自回归综合移动平均）模型进行传染病趋势预测。该模型特别适合处理具有季节性特征的时间序列数据，并结合了节假日因素和疾病特征参数进行预测。'
                        )
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '节假日权重'),
                        React.createElement('div', { style: style.text }, '模型考虑了不同月份的节假日对疾病传播的影响：'),
                        React.createElement('ul', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, '春节期间（1-2月）：权重1.2-1.5，人口流动频繁'),
                            React.createElement('li', { style: style.listItem }, '清明节（4月）：权重1.1，短期出行增加'),
                            React.createElement('li', { style: style.listItem }, '劳动节（5月）：权重1.2，人员流动密集'),
                            React.createElement('li', { style: style.listItem }, '暑假期间（7-8月）：权重1.1-1.2，学生放假、旅游高峰'),
                            React.createElement('li', { style: style.listItem }, '国庆节（10月）：权重1.3，长假人口流动大'),
                            React.createElement('li', { style: style.listItem }, '元旦前后（12-1月）：权重1.1-1.2，跨年活动增多')
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '疾病特征参数'),
                        React.createElement('div', { style: style.text }, '模型针对每种疾病的特定特征进行参数调整：'),
                        React.createElement('ul', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, '潜伏期（Incubation Period）：不同疾病的潜伏期从1天到数月不等'),
                            React.createElement('li', { style: style.listItem }, '基本传染数（R0）：表示一个感染者平均传染给其他人的数量'),
                            React.createElement('li', { style: style.listItem }, '季节性模式：每个月的传播概率权重，反映疾病的季节性特征'),
                            React.createElement('li', { style: style.listItem }, '传播类型：包括呼吸道传播、接触传播、媒介传播等')
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, 'SARIMA模型组成'),
                        React.createElement('div', { style: style.text }, 'SARIMA(p,d,q)(P,D,Q)s 模型包含以下组件：'),
                        React.createElement('ul', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, 'p：非季节性自回归阶数'),
                            React.createElement('li', { style: style.listItem }, 'd：非季节性差分阶数'),
                            React.createElement('li', { style: style.listItem }, 'q：非季节性移动平均阶数'),
                            React.createElement('li', { style: style.listItem }, 'P：季节性自回归阶数'),
                            React.createElement('li', { style: style.listItem }, 'D：季节性差分阶数'),
                            React.createElement('li', { style: style.listItem }, 'Q：季节性移动平均阶数'),
                            React.createElement('li', { style: style.listItem }, 's：季节周期长度')
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '预测流程'),
                        React.createElement('ol', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, '数据预处理：对原始数据进行季节性分解和差分处理'),
                            React.createElement('li', { style: style.listItem }, '参数识别：使用AIC/BIC准则自动选择最优模型参数'),
                            React.createElement('li', { style: style.listItem }, '特征整合：结合节假日权重和疾病特征参数'),
                            React.createElement('li', { style: style.listItem }, '模型训练：使用最大似然估计方法估计模型参数'),
                            React.createElement('li', { style: style.listItem }, '模型诊断：进行残差分析确保模型适用性'),
                            React.createElement('li', { style: style.listItem }, '预测生成：计算点预测值和95%置信区间')
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '置信区间说明'),
                        React.createElement('div', { style: style.text }, 
                            '预测结果包含95%置信区间，表示在给定历史数据的条件下，未来观测值有95%的概率落在该区间内。置信区间的宽度反映了预测的不确定性，预测期越远，不确定性越大，区间也越宽。'
                        )
                    ])
                ]);
            };

            const drawerRoot = document.createElement('div');
            drawerRoot.id = 'helpDrawerRoot';
            document.body.appendChild(drawerRoot);

            let visible = false;
            const toggleDrawer = () => {
                visible = !visible;
                render();
            };

            const render = () => {
                ReactDOM.render(
                    React.createElement(Drawer, {
                        visible: visible,
                        width: 600,
                        title: '传染病预测分析说明',
                        onCancel: toggleDrawer,
                        footer: null
                    }, React.createElement(DrawerContent)),
                    drawerRoot
                );
            };

            render();
            return toggleDrawer;
        }

        // React组件渲染函数
        function renderReactComponents() {
            const toggleDrawer = renderHelpDrawer();
            
            // 更新帮助图标，添加点击事件
            const helpIcon = document.querySelector('.help-icon i');
            if (helpIcon) {
                helpIcon.addEventListener('click', toggleDrawer);
            }

            // 预测指标选择
            const IndexSelect = () => {
                const options = [
                    { label: '感染人数', value: 'growth' },
                    { label: '死亡人数', value: 'deaths' },
                    { label: '治愈人数', value: 'cures' }
                ];

                return React.createElement(
                    Select,
                    {
                        mode: 'multiple',
                        placeholder: '选择指标',
                        style: { width: 320 },
                        defaultValue: window.selectedIndices,
                        allowClear: true,
                        size: "large",
                        onChange: (values) => {
                            window.selectedIndices = values;
                            // 如果图表已存在，更新图表显示
                            const chartDom = document.querySelector('.chart-container');
                            const chart = echarts.getInstanceByDom(chartDom);
                            if (chart) {
                                const series = chart.getOption().series;
                                series.forEach((s) => {
                                    let shouldShow = false;
                                    values.forEach(value => {
                                        if (s.name.includes(value === 'growth' ? '感染' : 
                                                          value === 'deaths' ? '死亡' : '治愈')) {
                                            shouldShow = true;
                                        }
                                    });
                                    chart.dispatchAction({
                                        type: shouldShow ? 'legendSelect' : 'legendUnSelect',
                                        name: s.name
                                    });
                                });
                            }
                        },
                        renderTag: ({ label, value, closable, onClose }) => {
                            return React.createElement(
                                Tag,
                                {
                                    closable: true,
                                    onClose: onClose,
                                    style: { 
                                        backgroundColor: 'var(--color-fill-2)',
                                        border: '1px solid var(--color-border)',
                                        marginRight: '8px',
                                        padding: '4px 8px',
                                    }
                                },
                                label
                            );
                        }
                    },
                    options.map(option =>
                        React.createElement(Select.Option, {
                            key: option.value,
                            value: option.value
                        }, option.label)
                    )
                );
            };

            // 渲染指标选择组件
            ReactDOM.render(
                React.createElement(IndexSelect),
                document.getElementById('indexSelect')
            );

            // 预测周期状态管理
            const PredictionPeriodControl = () => {
                const [value, setValue] = React.useState(6);
                
                const handleChange = (newValue) => {
                    setValue(newValue);
                    window.predictionPeriod = newValue; // 存储当前值到全局
                };

                return React.createElement(
                    'div',
                    {
                        style: {
                            display: 'flex',
                            alignItems: 'center',
                            gap: '20px'
                        }
                    },
                    React.createElement(InputNumber, {
                        min: 1,
                        max: 12,
                        value: value,
                        prefix: "预测周期",
                        suffix: "个月",
                        size: "large",
                        id: 'predictionPeriod',
                        style: { width: 160 },
                        onChange: handleChange
                    }),
                    React.createElement(arco.Slider, {
                        min: 1,
                        max: 12,
                        value: value,
                        style: { width: 200 },
                        id: 'periodSlider',
                        marks: {
                            1: '1',
                            3: '3',
                            6: '6',
                            9: '9',
                            12: '12'
                        },
                        formatTooltip: (val) => `${val}月`,
                        onChange: handleChange
                    })
                );
            };

            // 全局变量存储日期范围
            window.dateRange = [
                '2022-01-01',
                moment().endOf('month').format('YYYY-MM-DD')
            ];

            // 日期范围选择器
            ReactDOM.render(
                React.createElement(DatePicker.RangePicker, {
                    style: { width: 280 },
                    mode: 'month',
                    size: "large",
                    onChange: (valueString, value) => {
                        if (value && value.length === 2) {
                            window.dateRange = [
                                value[0].startOf('month').format('YYYY-MM-DD'),
                                value[1].endOf('month').format('YYYY-MM-DD')
                            ];
                        }
                    },
                    defaultValue: [
                        moment('2022-01-01'),
                        moment().endOf('month')
                    ],
                    shortcuts: [
                        {
                            text: '近3月',
                            value: () => [moment().subtract(3, 'months').startOf('month'), moment().endOf('month')]
                        },
                        {
                            text: '近6月',
                            value: () => [moment().subtract(6, 'months').startOf('month'), moment().endOf('month')]
                        },
                        {
                            text: '近1年',
                            value: () => [moment().subtract(1, 'year').startOf('month'), moment().endOf('month')]
                        },
                        {
                            text: '近2年',
                            value: () => [moment().subtract(2, 'year').startOf('month'), moment().endOf('month')]
                        },
                        {
                            text: '2022年至今',
                            value: () => [moment('2022-01-01'), moment().endOf('month')]
                        }
                    ]
                }),
                document.getElementById('dateRangePicker')
            );

            // 初始化预测周期的全变量
            window.predictionPeriod = 6;

            // 渲染预测周期控制组
            ReactDOM.render(
                React.createElement(PredictionPeriodControl),
                document.getElementById('predictionInput')
            );

            // 疾病选择下拉框
            ReactDOM.render(
                React.createElement(Select, {
                    placeholder: '选择传染病',
                    style: { width: 180 },
                    allowClear: true,
                    showSearch: true,
                    size: "large",
                    id: 'disease',
                    onChange: (value) => {
                        selectedDisease = value;  // 只使用一个变量
                    }
                }),
                document.getElementById('diseaseSelect')
            );

            // 生成预测按钮
            ReactDOM.render(
                React.createElement('div', {
                    style: {
                        display: 'flex',
                        gap: '12px'
                    }
                }, [
                    React.createElement(Button, {
                        type: 'primary',
                        icon: React.createElement('i', { className: 'arco-icon-line-chart' }),
                        onClick: handleGenerate,
                        size: "large"
                    }, "生成预测"),
                    React.createElement(Button, {
                        type: 'primary',
                        status: 'success',
                        icon: React.createElement('i', { className: 'arco-icon-line-chart' }),
                        onClick: handleGenerateReport,
                        size: "large"
                    }, "生成报告")
                ]),
                document.getElementById('generateButton')
            );
        }

        // 渲染顶部菜单
        function renderTopMenu() {
            const { Menu } = arco;
            const MenuItem = Menu.Item;

            const menuComponent = React.createElement(
                'div',
                { className: 'menu-demo' },
                React.createElement(
                    Menu,
                    { mode: 'horizontal', defaultSelectedKeys: ['2'] },
                    React.createElement(
                        MenuItem,
                        {
                            key: '0',
                            style: { padding: 0, marginRight: 8 },
                            disabled: true
                        }
                    ),
                    React.createElement(MenuItem, { key: '1', onClick: () => window.location.href = '/'}, '首页'),
                    React.createElement(MenuItem, { key: '2', onClick: () => window.location.href = '/disease_prediction_china' }, '传染病预测'),
                    React.createElement(MenuItem, { key: '3', onClick: () => window.location.href = '/cdc_news_china' }, '疾控中心数据')
                )
            );

            ReactDOM.createRoot(document.getElementById('menu-container')).render(menuComponent);
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            renderTopMenu();
            renderReactComponents();
            renderLoadingSpinner();

            // 加载疾病数据
            $.ajax({
                url: '/get_diseases_china',
                method: 'GET',
                timeout: 10000,
                success: function(data) {
                    if (!data || data.error) {
                        console.error('Failed to load diseases:', data.error);
                        Message.error('加载疾病列表失败: ' + (data.error || '未知错误'));
                        return;
                    }
                    
                    if (data.diseases && Array.isArray(data.diseases)) {
                        const options = data.diseases.map(disease => ({
                            label: disease.name,
                            value: disease.value
                        }));
                        
                        ReactDOM.render(
                            React.createElement(Select, {
                                placeholder: '请选择传染病名称',
                                style: { width: 240 },
                                allowClear: true,
                                showSearch: true,
                                prefix: "传染病名称",
                                size: "large",
                                id: 'disease',
                                options: options,
                                onChange: (value) => {
                                    selectedDisease = value;  // 只使用一个变量
                                }
                            }),
                            document.getElementById('diseaseSelect')
                        );
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Failed to load diseases:', error);
                    let errorMessage = '加载疾病列表失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage += ': ' + xhr.responseJSON.error;
                    }
                    Message.error(errorMessage);
                }
            });
        });

        // 设置预测周期
        function setPredictionPeriod(months) {
            const inputNumber = document.querySelector('#predictionPeriod');
            if (inputNumber) {
                inputNumber.value = months;
            }
        }

        // 渲染加载动画
        function renderLoadingSpinner() {
            ReactDOM.render(
                React.createElement(Spin, {
                    dot: true,
                    size: 'large',
                    style: { fontSize: '32px' }
                }),
                document.getElementById('loadingSpinner')
            );
        }

        // 显示加载动画
        function showLoading() {
            document.querySelector('.loading-overlay').classList.add('active');
        }

        // 隐藏加载动画
        function hideLoading() {
            document.querySelector('.loading-overlay').classList.remove('active');
        }

        // 处理生成预测
        function handleGenerate() {
            if (!selectedDisease) {  // 使用 selectedDisease
                Message.error('请选择传染病');
                return;
            }

            const generateBtn = document.querySelector('#generateButton button');
            generateBtn.setAttribute('loading', 'true');
            generateBtn.disabled = true;

            // 显示加载动画
            showLoading();

            // 获取日期范围，如果没有选择则使用默认值（2022年1月到今天）
            const defaultEndDate = moment().format('YYYY-MM-DD');
            const defaultStartDate = '2022-01-01';
            const startDate = window.dateRange ? window.dateRange[0] : defaultStartDate;
            const endDate = window.dateRange ? window.dateRange[1] : defaultEndDate;

            $.ajax({
                url: '/predict_disease_china',
                method: 'POST',
                data: {
                    disease: selectedDisease,  // 使用 selectedDisease
                    start_date: startDate,
                    end_date: endDate,
                    prediction_period: window.predictionPeriod
                },
                success: function(response) {
                    generateBtn.removeAttribute('loading');
                    generateBtn.disabled = false;
                    hideLoading();
                    updateChart(response);
                },
                error: function(xhr, status, error) {
                    let errorMessage = '生成预测失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    Message.error(errorMessage);
                    generateBtn.removeAttribute('loading');
                    generateBtn.disabled = false;
                    hideLoading();
                }
            });
        }

        // 更新图表
        function updateChart(data) {
            // 保存数据到全局变量
            chartData = {
                actual: data.historical_dates.map((date, index) => ({
                    date: date,
                    infected: data.historical_data.growth[index],
                    dead: data.historical_data.deaths[index],
                    cured: data.historical_data.cures[index]
                })),
                predicted: data.dates.map((date, index) => ({
                    date: date,
                    infected: data.predictions.growth.mean[index],
                    dead: data.predictions.deaths.mean[index],
                    cured: data.predictions.cures.mean[index]
                }))
            };
            
            startDate = data.historical_dates[0];
            endDate = data.dates[data.dates.length - 1];

            // 数据验证
            if (!data || typeof data !== 'object') {
                Message.error('返回数据格式错误');
                return;
            }

            // 检查必要的数据字段
            const requiredFields = ['historical_dates', 'dates', 'historical_data', 'predictions'];
            const missingFields = requiredFields.filter(field => !data[field]);
            if (missingFields.length > 0) {
                Message.error('数据结构不完整: ' + missingFields.join(', '));
                return;
            }

            // 检查数据类型
            if (!Array.isArray(data.historical_dates) || !Array.isArray(data.dates)) {
                Message.error('日期数据类型错误');
                return;
            }

            if (!data.historical_data || !data.predictions || 
                !data.predictions.growth || !data.predictions.deaths || !data.predictions.cures) {
                Message.error('预测数据结构不完整');
                return;
            }

            const chartDom = document.querySelector('.chart-container');
            if (!chartDom) {
                Message.error('图表容器不存在');
                return;
            }

            // 销毁现有的图表实例（如果存在）
            const existingChart = echarts.getInstanceByDom(chartDom);
            if (existingChart) {
                existingChart.dispose();
            }

            // 初始化新的图表实例
            const myChart = echarts.init(chartDom);
            
            try {
                // 合并历史数据和预测数据的日期
                const allDates = [...data.historical_dates, ...data.dates];
                
                // 准备数据系列
                const series = [
                    // 感染人数
                    {
                        name: '感染预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.growth.upper],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#5470c6',
                            origin: 'end'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '感染预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.growth.lower],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#5470c6',
                            origin: 'start'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '历史感染人数',
                        type: 'line',
                        data: data.historical_data.growth,
                        lineStyle: { width: 2 },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#5470c6' }
                    },
                    {
                        name: '预测感染人数',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length-1).fill(null), data.historical_data.growth[data.historical_data.growth.length-1], ...data.predictions.growth.mean],
                        lineStyle: { width: 2, type: 'dashed' },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#5470c6' }
                    },
                    
                    // 死亡人数
                    {
                        name: '死亡预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.deaths.upper],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#ee6666',
                            origin: 'end'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '死亡预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.deaths.lower],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#ee6666',
                            origin: 'start'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '历史死亡人数',
                        type: 'line',
                        data: data.historical_data.deaths,
                        lineStyle: { width: 2 },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#ee6666' }
                    },
                    {
                        name: '预测死亡人数',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length-1).fill(null), data.historical_data.deaths[data.historical_data.deaths.length-1], ...data.predictions.deaths.mean],
                        lineStyle: { width: 2, type: 'dashed' },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#ee6666' }
                    },
                    
                    // 治愈人数
                    {
                        name: '治愈预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.cures.upper],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#73c0de',
                            origin: 'end'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '治愈预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.cures.lower],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#73c0de',
                            origin: 'start'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '历史治愈人数',
                        type: 'line',
                        data: data.historical_data.cures,
                        lineStyle: { width: 2 },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#73c0de' }
                    },
                    {
                        name: '预测治愈人数',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length-1).fill(null), data.historical_data.cures[data.historical_data.cures.length-1], ...data.predictions.cures.mean],
                        lineStyle: { width: 2, type: 'dashed' },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#73c0de' }
                    }
                ];

                const option = {
                    title: {
                        text: '疾病预测分析',
                        subtext: '基于SARIMA模型',
                        left: 'center',
                        top: 10,
                        padding: [0, 0, 10, 0]
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross'
                        },
                        formatter: function(params) {
                            let result = params[0].axisValueLabel + '<br/>';
                            
                            // 判断是否是预测数据
                            const isPrediction = params[0].axisValueLabel >= data.dates[0];
                            
                            // 按照类型分组显示数据
                            const groups = {
                                '感染': { color: '#5470c6', data: [], ci: {} },
                                '死亡': { color: '#ee6666', data: [], ci: {} },
                                '治愈': { color: '#73c0de', data: [], ci: {} }
                            };
                            
                            params.forEach(param => {
                                // 处理置信区间数据
                                if (param.seriesName.includes('预测区间')) {
                                    Object.keys(groups).forEach(key => {
                                        if (param.seriesName.includes(key)) {
                                            if (param.value !== null) {
                                                if (!groups[key].ci.upper || param.value > groups[key].ci.upper) {
                                                    groups[key].ci.upper = param.value;
                                                }
                                                if (!groups[key].ci.lower || param.value < groups[key].ci.lower) {
                                                    groups[key].ci.lower = param.value;
                                                }
                                            }
                                        }
                                    });
                                    return;
                                }
                                
                                // 只显示历史数据或预测数据
                                const isParamPrediction = param.seriesName.includes('预测');
                                if (isPrediction !== isParamPrediction) {
                                    return;
                                }
                                
                                Object.keys(groups).forEach(key => {
                                    if (param.seriesName.includes(key)) {
                                        groups[key].data.push({
                                            name: param.seriesName,
                                            value: param.value
                                        });
                                    }
                                });
                            });
                            
                            // 生成tooltip内容
                            Object.keys(groups).forEach(key => {
                                if (groups[key].data.length > 0) {
                                    result += '<div style="margin: 10px 0;">';
                                    result += `<span style="color:${groups[key].color}">● ${key}：</span><br/>`;
                                    groups[key].data.forEach(item => {
                                        if (item.value !== null) {
                                            result += `${item.name}：${item.value >= 10000 ? (item.value/10000).toFixed(2) + '万' : item.value}<br/>`;
                                        }
                                    });
                                    // 如果是预测数据，显示置信区间
                                    if (isPrediction && groups[key].ci.upper !== undefined && groups[key].ci.lower !== undefined) {
                                        const upperValue = groups[key].ci.upper >= 10000 ? (groups[key].ci.upper/10000).toFixed(2) + '万' : groups[key].ci.upper;
                                        const lowerValue = groups[key].ci.lower >= 10000 ? (groups[key].ci.lower/10000).toFixed(2) + '万' : groups[key].ci.lower;
                                        result += `95%置信区间：[${lowerValue}, ${upperValue}]<br/>`;
                                    }
                                    result += '</div>';
                                }
                            });
                            
                            return result;
                        }
                    },
                    legend: {
                        data: ['历史感染人数', '预测感染人数', '历史死亡人数', '预测死亡人数', '历史治愈人数', '预测治愈人数'],
                        bottom: 10,
                        selected: {
                            '历史感染人数': window.selectedIndices.includes('growth'),
                            '预测感染人数': window.selectedIndices.includes('growth'),
                            '感染预测区间': window.selectedIndices.includes('growth'),
                            '历史死亡人数': window.selectedIndices.includes('deaths'),
                            '预测死亡人数': window.selectedIndices.includes('deaths'),
                            '死亡预测区间': window.selectedIndices.includes('deaths'),
                            '历史治愈人数': window.selectedIndices.includes('cures'),
                            '预测治愈人数': window.selectedIndices.includes('cures'),
                            '治愈预测区间': window.selectedIndices.includes('cures')
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: allDates,
                        axisLabel: {
                            rotate: 45,
                            formatter: function(value) {
                                return value.substring(0, 7); // 显示年月
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '人数',
                        axisLabel: {
                            formatter: function(value) {
                                if (value >= 10000) {
                                    return (value / 10000).toFixed(1) + '万';
                                }
                                return value;
                            }
                        }
                    },
                    series: series,
                    dataZoom: [
                        {
                            type: 'slider',
                            show: true,
                            start: 0,
                            end: 100,
                            bottom: 50
                        },
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        }
                    ]
                };

                myChart.setOption(option);
                
                // 处理窗口大小变化
                window.addEventListener('resize', () => {
                    if (myChart) {
                        myChart.resize();
                    }
                });
            } catch (error) {
                Message.error('更新图表时发生错误');
            }
        }

        // 页面加载完成后初始化图表容器
        document.addEventListener('DOMContentLoaded', function() {
            const chartDom = document.querySelector('.chart-container');
            if (chartDom) {
                const myChart = echarts.init(chartDom);
                myChart.setOption({
                    title: {
                        text: '请选择疾病并生成预测',
                        left: 'center',
                        top: '40%'
                    }
                });
            }
        });

        function renderHelpIcon() {
            ReactDOM.render(
                React.createElement(Tooltip, {
                    content: '图表说明：实线表示历史数据，虚线表示预测数据，浅色区域表示预测的95%置信区间',
                    position: 'top'
                }, 
                    React.createElement('i', {
                        className: 'fa-solid fa-circle-question',
                        style: {
                            fontSize: '20px',
                            color: '#1d2129',
                            cursor: 'pointer',
                            opacity: '0.8',
                            transition: 'opacity 0.2s'
                        },
                        onMouseEnter: (e) => e.target.style.opacity = '1',
                        onMouseLeave: (e) => e.target.style.opacity = '0.8'
                    })
                ),
                document.getElementById('chartHelpIcon')
            );
        }

        // 添加辅助函数来更新下载按钮状态
        function enableDownloadButton() {
            const btnElement = document.getElementById('report-download-btn');
            if (btnElement) {
                // 直接替换按钮元素
                const newButton = document.createElement('button');
                newButton.id = 'report-download-btn';
                newButton.className = 'arco-btn arco-btn-primary report-download-button';
                newButton.style.marginRight = '8px';
                newButton.textContent = '下载报告';
                newButton.onclick = () => downloadReport(fullReportContent);
                
                // 替换旧按钮
                if (btnElement.parentNode) {
                    btnElement.parentNode.replaceChild(newButton, btnElement);
                    console.log('下载按钮已替换为可用状态');
                }
            }
        }

        // 处理生成报告
        async function handleGenerateReport() {
            if (!selectedDisease) {
                Message.error('请选择传染病');
                return;
            }

            if (!chartData) {
                Message.error('请先生成预测数据');
                return;
            }

            // 显示模态框并获取内容元素
            const reportContentElement = await showReportModal();
            if (!reportContentElement) {
                Message.error('创建报告窗口失败');
                return;
            }

            reportContentElement.textContent = '正在生成报告，请稍候...';

            // 准备请求数据
            const requestData = {
                disease_name: selectedDisease,
                start_date: startDate,
                end_date: endDate,
                actual_data: chartData.actual,
                predicted_data: chartData.predicted
            };

            try {
                const response = await fetch('/api/generate_report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    let errorMsg = `HTTP error! status: ${response.status}`;
                    try {
                        const errData = await response.json();
                        errorMsg = errData.error || errorMsg;
                    } catch (e) {}
                    throw new Error(errorMsg);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let firstChunk = true;

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        // 流式输出结束，启用下载按钮
                        console.log('流式输出完成，正在启用下载按钮');
                        enableDownloadButton(); // 使用新的函数
                        break;
                    }

                    const chunk = decoder.decode(value, { stream: true });
                    
                    try {
                        const potentialError = JSON.parse(chunk);
                        if (potentialError.error) {
                            Message.error(`报告生成错误: ${potentialError.error}`);
                            reportContentElement.textContent = `错误: ${potentialError.error}`;
                            return;
                        }
                    } catch (e) {}

                    if (firstChunk) {
                        reportContentElement.updateContent(chunk);
                        firstChunk = false;
                    } else {
                        reportContentElement.updateContent(chunk);
                    }
                    
                    // 自动滚动到底部
                    reportContentElement.scrollTop = reportContentElement.scrollHeight;
                }

            } catch (error) {
                console.error('生成报告失败:', error);
                Message.error(error.message || '生成报告失败');
                reportContentElement.textContent = `错误: ${error.message}`;
            }
        }

        // 修改：只创建并显示模态框，返回内容区域的引用
        function showReportModal() {
            const { Modal, Button } = arco;
            const reportId = 'streaming-report-content';
            fullReportContent = ''; // 重置全局变量
            let modalInstance = null;

            // 创建下载按钮
            const downloadButton = React.createElement(Button, {
                type: 'primary',
                onClick: () => downloadReport(fullReportContent),
                style: { marginRight: '8px' },
                id: 'report-download-btn',
                disabled: true, // 初始状态禁用
                className: 'report-download-button' // 添加类名便于选择
            }, '下载报告');

            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                .think-container {
                    margin: 10px 0;
                    border: 1px solid var(--color-border);
                    border-radius: 4px;
                    overflow: hidden;
                }
                .think-header {
                    background-color: var(--color-fill-2);
                    padding: 8px 12px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    user-select: none;
                }
                .think-header:hover {
                    background-color: var(--color-fill-3);
                }
                .think-content {
                    padding: 12px;
                    background-color: var(--color-fill-1);
                    white-space: pre-wrap;
                    display: none;
                }
                .think-content.expanded {
                    display: block;
                }
                .think-toggle {
                    color: var(--color-text-3);
                    font-size: 12px;
                }
                .non-think-content {
                    white-space: pre-wrap;
                    margin: 10px 0;
                }
            `;
            document.head.appendChild(style);

            const modalContent = React.createElement(
                'div',
                null,
                [
                    React.createElement(
                        'div', 
                        { 
                            id: reportId,
                            style: { 
                                maxHeight: '400px', 
                                overflow: 'auto',
                                backgroundColor: 'var(--color-fill-2)',
                                padding: '16px',
                                borderRadius: '4px',
                                fontSize: '14px',
                                lineHeight: '1.6',
                                marginBottom: '16px'
                            } 
                        }, 
                        '' // 初始为空
                    )
                ]
            );

            modalInstance = Modal.confirm({
                title: ' 预测分析报告',
                icon: React.createElement('i', { 
                    className: 'fa-solid fa-square-poll-vertical', 
                    style: { color: 'var(--primary-color)' } 
                }),
                content: modalContent,
                style: { width: '800px' },
                footer: React.createElement(
                    'div',
                    { style: { display: 'flex', justifyContent: 'flex-end' } },
                    [
                        downloadButton,
                        React.createElement(
                            Button,
                            {
                                type: "primary",
                                onClick: () => {
                                    Modal.confirm({
                                        title: '确认关闭',
                                        content: '确定要关闭报告吗？',
                                        onOk: () => modalInstance.close(),
                                    });
                                }
                            },
                            '关闭'
                        )
                    ]
                ),
                closable: false,
                maskClosable: false,
                onCancel: (e) => {
                    e.preventDefault();
                    Modal.confirm({
                        title: '确认关闭',
                        content: '确定要关闭报告吗？',
                        onOk: () => modalInstance.close(),
                    });
                }
            });

            // 等待React渲染完成并获取DOM元素
            return new Promise(resolve => {
                setTimeout(() => {
                    const element = document.getElementById(reportId);
                    if (element) {
                        // 添加更新内容的方法
                        element.updateContent = (chunk) => {
                            fullReportContent += chunk;

                            // 先分割出所有 <think> ... </think> 段落
                            let parts = [];
                            let lastIndex = 0;
                            const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
                            let match;
                            while ((match = thinkRegex.exec(fullReportContent)) !== null) {
                                // 非思考部分
                                if (match.index > lastIndex) {
                                    parts.push({
                                        type: 'non-think',
                                        content: fullReportContent.slice(lastIndex, match.index)
                                    });
                                }
                                // 思考部分
                                parts.push({
                                    type: 'think',
                                    content: match[1]
                                });
                                lastIndex = thinkRegex.lastIndex;
                            }
                            // 最后剩余的非思考部分
                            if (lastIndex < fullReportContent.length) {
                                parts.push({
                                    type: 'non-think',
                                    content: fullReportContent.slice(lastIndex)
                                });
                            }

                            // 组装 HTML
                            let html = '';
                            parts.forEach(part => {
                                if (part.type === 'think') {
                                    const thinkId = 'think-' + Math.random().toString(36).substr(2, 9);
                                    html += `
                                        <div class="think-container">
                                            <div class="think-header" data-think-id="${thinkId}">
                                                <span>思考过程</span>
                                                <span class="think-toggle">点击展开</span>
                                            </div>
                                            <div id="${thinkId}" class="think-content">${part.content}</div>
                                        </div>
                                    `;
                                } else {
                                    // 保证换行
                                    html += `<div class="non-think-content">${part.content.replace(/\n/g, '<br>')}</div>`;
                                }
                            });

                            element.innerHTML = html;

                            // 重新绑定所有思考头的点击事件
                            Array.from(element.getElementsByClassName('think-header')).forEach(header => {
                                const thinkId = header.getAttribute('data-think-id');
                                const content = document.getElementById(thinkId);
                                const toggle = header.querySelector('.think-toggle');
                                header.onclick = () => {
                                    content.classList.toggle('expanded');
                                    toggle.textContent = content.classList.contains('expanded') ? '点击折叠' : '点击展开';
                                };
                            });
                        };
                        resolve(element);
                    } else {
                        console.error("Could not find report content element after modal creation.");
                        resolve(null);
                    }
                }, 100);
            });
        }
        
        function downloadReport(textContent) {
            const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `传染病预测分析报告_${selectedDisease}_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html> 