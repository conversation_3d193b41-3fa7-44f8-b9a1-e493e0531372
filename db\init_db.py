"""
SQLite数据库初始化工具
"""
import os
import sys
import sqlite3
from flask import Flask
import argparse
from datetime import datetime

# 添加项目根目录到系统路径，确保可以导入其他模块
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_ROOT)

# 先导入配置
from db.db_config import Config, SQLITE_DB_PATH
from models.models import db, Disease, DisasterChina

# 获取数据目录和数据库名称
DATA_DIR = os.path.dirname(SQLITE_DB_PATH)
DB_NAME = os.path.basename(SQLITE_DB_PATH)

def create_sqlite_tables(app=None):
    """创建SQLite数据库表结构"""
    if app is None:
        # 创建Flask应用
        app = Flask(__name__)
        app.config.from_object(Config)
        db.init_app(app)
    
    print("正在创建SQLite数据库表结构...")
    
    try:
        # 初始化数据库
        with app.app_context():
            db.create_all()
            print("SQLite数据库表结构创建完成")
    except Exception as e:
        print(f"创建数据库表结构时出错: {str(e)}")
        raise
    
    return app

def initialize_sample_data(app):
    """初始化样本数据"""
    try:
        with app.app_context():
            # 检查是否已有数据
            if Disease.query.count() == 0:
                # 添加一些疾病样本数据
                sample_diseases = [
                    Disease(data_value="COVID-19", name="新冠肺炎"),
                    Disease(data_value="INFLUENZA", name="流感"),
                    Disease(data_value="H1N1", name="甲型H1N1流感")
                ]
                db.session.add_all(sample_diseases)
                
                # 添加一些灾害样本数据
                now = datetime.now()
                sample_disasters = [
                    DisasterChina(
                        time=now,
                        disaster="COVID-19",
                        disaster_type="传染病",
                        sub_disaster="SARS-CoV-2",
                        cities="全国",
                        source_url="http://www.nhc.gov.cn",
                        growth=100,
                        deaths=2,
                        cures=80,
                        sub_growth=100,
                        sub_deaths=2,
                        sub_cures=80,
                        update_time=now
                    )
                ]
                db.session.add_all(sample_disasters)
                
                # 提交事务
                db.session.commit()
                print("样本数据初始化完成")
            else:
                print("数据库中已有数据，跳过样本数据初始化")
    except Exception as e:
        db.session.rollback()
        print(f"初始化样本数据时出错: {str(e)}")
        raise

def check_db_exists():
    """检查数据库是否已存在"""
    return os.path.exists(SQLITE_DB_PATH)

def init_db(force=False, add_sample_data=False):
    """初始化SQLite数据库
    
    参数:
        force (bool): 如果为True，则强制重新创建数据库，即使数据库已存在
        add_sample_data (bool): 如果为True，添加样本数据
    """
    # 显示数据库配置信息
    print(f"数据库名称: {DB_NAME}")
    print(f"数据目录: {DATA_DIR}")
    print(f"数据库完整路径: {SQLITE_DB_PATH}")
    
    # 检查并创建数据目录
    if not os.path.exists(DATA_DIR):
        os.makedirs(DATA_DIR)
        print(f"创建数据目录: {DATA_DIR}")
    else:
        print(f"数据目录已存在: {DATA_DIR}")
    
    # 检查数据库是否已存在
    if check_db_exists() and not force:
        print(f"数据库文件已存在: {SQLITE_DB_PATH}")
        print("如需重新创建数据库，请使用 --force 参数")
        return None
    
    # 如果强制重建且文件存在，先删除旧文件
    if check_db_exists() and force:
        try:
            os.remove(SQLITE_DB_PATH)
            print(f"已删除旧数据库文件: {SQLITE_DB_PATH}")
        except OSError as e:
            print(f"删除旧数据库文件时出错: {str(e)}")
            return None
    
    try:
        # 创建Flask应用
        app = Flask(__name__)
        app.config.from_object(Config)
        db.init_app(app)
        
        # 创建数据库表结构
        app = create_sqlite_tables(app)
        
        # 初始化样本数据
        if add_sample_data:
            initialize_sample_data(app)
        
        print(f"SQLite数据库初始化完成: {SQLITE_DB_PATH}")
        return app
    except Exception as e:
        print(f"初始化数据库时出错: {str(e)}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SQLite数据库初始化工具')
    parser.add_argument('--force', action='store_true', help='强制重新创建数据库')
    parser.add_argument('--sample-data', action='store_true', help='添加样本数据')
    args = parser.parse_args()
    
    # 初始化数据库
    app = init_db(force=args.force, add_sample_data=args.sample_data)
    if not app:
        sys.exit(1)

if __name__ == "__main__":
    main()
