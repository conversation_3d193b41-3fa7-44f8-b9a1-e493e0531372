from flask import jsonify, request
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sqlite3
from statsmodels.tsa.statespace.sarimax import SARIMAX
from db.db_config import SQLITE_DB_PATH
import traceback

class SARIMAPredictionChina:
    def __init__(self):
        # 定义月度假期权重
        self.holiday_weights = {
            1: 1.2,  # 元旦+春节前
            2: 1.5,  # 春节
            3: 1.0,
            4: 1.1,  # 清明节
            5: 1.2,  # 劳动节
            6: 1.0,
            7: 1.1,  # 暑假开始
            8: 1.2,  # 暑假
            9: 1.0,
            10: 1.3, # 国庆节
            11: 1.0,
            12: 1.1  # 元旦前
        }
        
        # 定义季节性疾病特征
        self.disease_characteristics = {
            '新型冠状病毒肺炎': {
                'incubation_period': 5,  # 1-14天，中位数5天
                'r0': 2.5,  # 2.0-3.0
                'seasonal_pattern': {
                    1: 1.4, 2: 1.3, 3: 1.2, 4: 1.0,  # 冬春季节高发
                    5: 0.8, 6: 0.7, 7: 0.8, 8: 0.9,
                    9: 1.0, 10: 1.1, 11: 1.2, 12: 1.3
                },
                'transmission_type': 'respiratory'
            },
            '流行性感冒': {
                'incubation_period': 2,  # 1-4天
                'r0': 1.5,  # 1.2-1.8
                'seasonal_pattern': {
                    1: 1.5, 2: 1.4, 3: 1.2, 4: 1.0,
                    5: 0.7, 6: 0.6, 7: 0.6, 8: 0.7,
                    9: 0.9, 10: 1.1, 11: 1.3, 12: 1.4
                },
                'transmission_type': 'respiratory'
            },
            '手足口病': {
                'incubation_period': 4,  # 3-7天
                'r0': 2.5,  # 2.0-3.0
                'seasonal_pattern': {
                    1: 0.7, 2: 0.8, 3: 1.0, 4: 1.2,
                    5: 1.4, 6: 1.5, 7: 1.4, 8: 1.3,
                    9: 1.1, 10: 0.9, 11: 0.8, 12: 0.7
                },
                'transmission_type': 'contact'
            },
            '病毒性肝炎': {
                'incubation_period': 30,  # 15-45天（乙肝）
                'r0': 1.8,  # 1.5-2.0
                'seasonal_pattern': {
                    1: 1.0, 2: 1.0, 3: 1.1, 4: 1.1,
                    5: 1.2, 6: 1.2, 7: 1.1, 8: 1.1,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'blood_contact'
            },
            '肺结核': {
                'incubation_period': 42,  # 2-12周
                'r0': 1.0,  # 0.8-1.2
                'seasonal_pattern': {
                    1: 1.2, 2: 1.2, 3: 1.1, 4: 1.0,
                    5: 0.9, 6: 0.8, 7: 0.8, 8: 0.9,
                    9: 1.0, 10: 1.1, 11: 1.1, 12: 1.2
                },
                'transmission_type': 'respiratory'
            },
            '艾滋病': {
                'incubation_period': 21,  # 急性期2-4周
                'r0': 2.0,  # 变异较大
                'seasonal_pattern': {
                    1: 1.0, 2: 1.0, 3: 1.0, 4: 1.0,
                    5: 1.0, 6: 1.0, 7: 1.0, 8: 1.0,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'blood_contact'
            },
            '麻疹': {
                'incubation_period': 10,  # 7-14天
                'r0': 12.0,  # 12-18
                'seasonal_pattern': {
                    1: 1.3, 2: 1.4, 3: 1.3, 4: 1.2,
                    5: 1.0, 6: 0.8, 7: 0.7, 8: 0.8,
                    9: 0.9, 10: 1.0, 11: 1.1, 12: 1.2
                },
                'transmission_type': 'respiratory'
            },
            '流行性腮腺炎': {
                'incubation_period': 18,  # 16-18天
                'r0': 4.0,  # 4-7
                'seasonal_pattern': {
                    1: 1.3, 2: 1.4, 3: 1.3, 4: 1.2,
                    5: 1.0, 6: 0.8, 7: 0.7, 8: 0.8,
                    9: 0.9, 10: 1.0, 11: 1.1, 12: 1.2
                },
                'transmission_type': 'respiratory'
            },
            '登革热': {
                'incubation_period': 5,  # 3-14天
                'r0': 2.0,  # 变异较大
                'seasonal_pattern': {
                    1: 0.7, 2: 0.7, 3: 0.8, 4: 1.0,
                    5: 1.2, 6: 1.4, 7: 1.5, 8: 1.4,
                    9: 1.2, 10: 1.0, 11: 0.8, 12: 0.7
                },
                'transmission_type': 'vector'
            },
            '人感染H7N9禽流感': {
                'incubation_period': 7,  # 1-10天
                'r0': 0.8,  # <1，主要为禽传人
                'seasonal_pattern': {
                    1: 1.4, 2: 1.3, 3: 1.2, 4: 1.1,
                    5: 0.8, 6: 0.7, 7: 0.7, 8: 0.8,
                    9: 0.9, 10: 1.0, 11: 1.2, 12: 1.3
                },
                'transmission_type': 'zoonotic'
            },
            '狂犬病': {
                'incubation_period': 40,  # 通常1-3个月
                'r0': 0.1,  # 极少人传人
                'seasonal_pattern': {
                    1: 1.0, 2: 1.0, 3: 1.0, 4: 1.0,
                    5: 1.1, 6: 1.2, 7: 1.2, 8: 1.1,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'zoonotic'
            },
            '霍乱': {
                'incubation_period': 2,  # 几小时至5天
                'r0': 1.5,  # 1.0-2.0
                'seasonal_pattern': {
                    1: 0.7, 2: 0.7, 3: 0.8, 4: 1.0,
                    5: 1.2, 6: 1.4, 7: 1.5, 8: 1.4,
                    9: 1.2, 10: 1.0, 11: 0.8, 12: 0.7
                },
                'transmission_type': 'water_food'
            },
            '细菌性和阿米巴性痢疾': {
                'incubation_period': 3,  # 1-7天
                'r0': 2.0,  # 1.5-2.5
                'seasonal_pattern': {
                    1: 0.7, 2: 0.7, 3: 0.8, 4: 1.0,
                    5: 1.2, 6: 1.4, 7: 1.5, 8: 1.4,
                    9: 1.2, 10: 1.0, 11: 0.8, 12: 0.7
                },
                'transmission_type': 'water_food'
            },
            '布鲁氏菌病': {
                'incubation_period': 14,  # 5-60天
                'r0': 0.5,  # 极少人传人
                'seasonal_pattern': {
                    1: 0.8, 2: 0.8, 3: 0.9, 4: 1.0,
                    5: 1.2, 6: 1.3, 7: 1.3, 8: 1.2,
                    9: 1.1, 10: 1.0, 11: 0.9, 12: 0.8
                },
                'transmission_type': 'zoonotic'
            },
            '疟疾': {
                'incubation_period': 12,  # 7-30天
                'r0': 1.0,  # 依地区而异
                'seasonal_pattern': {
                    1: 0.7, 2: 0.7, 3: 0.8, 4: 1.0,
                    5: 1.2, 6: 1.4, 7: 1.5, 8: 1.4,
                    9: 1.2, 10: 1.0, 11: 0.8, 12: 0.7
                },
                'transmission_type': 'vector'
            },
            '丝虫病': {
                'incubation_period': 90,  # 3-6个月
                'r0': 0.5,  # 通过蚊虫传播
                'seasonal_pattern': {
                    1: 0.7, 2: 0.7, 3: 0.8, 4: 1.0,
                    5: 1.2, 6: 1.4, 7: 1.5, 8: 1.4,
                    9: 1.2, 10: 1.0, 11: 0.8, 12: 0.7
                },
                'transmission_type': 'vector'
            },
            '人感染高致病性禽流感': {
                'incubation_period': 5,  # 2-7天
                'r0': 0.6,  # 主要禽传人
                'seasonal_pattern': {
                    1: 1.4, 2: 1.3, 3: 1.2, 4: 1.1,
                    5: 0.8, 6: 0.7, 7: 0.7, 8: 0.8,
                    9: 0.9, 10: 1.0, 11: 1.2, 12: 1.3
                },
                'transmission_type': 'zoonotic'
            },
            '传染性非典型肺炎': {
                'incubation_period': 5,  # 2-7天
                'r0': 3.0,  # 2-4
                'seasonal_pattern': {
                    1: 1.2, 2: 1.2, 3: 1.3, 4: 1.2,
                    5: 1.0, 6: 0.8, 7: 0.8, 8: 0.9,
                    9: 1.0, 10: 1.1, 11: 1.1, 12: 1.2
                },
                'transmission_type': 'respiratory'
            },
            '伤寒和副伤寒': {
                'incubation_period': 14,  # 7-21天
                'r0': 2.0,  # 1.5-2.5
                'seasonal_pattern': {
                    1: 0.8, 2: 0.8, 3: 0.9, 4: 1.1,
                    5: 1.2, 6: 1.3, 7: 1.3, 8: 1.2,
                    9: 1.1, 10: 1.0, 11: 0.9, 12: 0.8
                },
                'transmission_type': 'water_food'
            },
            '其他感染性腹泻病': {
                'incubation_period': 2,  # 1-3天
                'r0': 1.8,  # 1.5-2.0
                'seasonal_pattern': {
                    1: 0.8, 2: 0.8, 3: 0.9, 4: 1.0,
                    5: 1.2, 6: 1.3, 7: 1.4, 8: 1.3,
                    9: 1.1, 10: 1.0, 11: 0.9, 12: 0.8
                },
                'transmission_type': 'water_food'
            },
            '包虫病': {
                'incubation_period': 180,  # 数月至数年
                'r0': 0.2,  # 极少人传人
                'seasonal_pattern': {
                    1: 1.0, 2: 1.0, 3: 1.0, 4: 1.0,
                    5: 1.0, 6: 1.0, 7: 1.0, 8: 1.0,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'zoonotic'
            },
            '急性出血性结膜炎': {
                'incubation_period': 1,  # 24-48小时
                'r0': 3.0,  # 2.5-3.5
                'seasonal_pattern': {
                    1: 0.8, 2: 0.8, 3: 0.9, 4: 1.0,
                    5: 1.2, 6: 1.4, 7: 1.4, 8: 1.3,
                    9: 1.1, 10: 1.0, 11: 0.9, 12: 0.8
                },
                'transmission_type': 'contact'
            },
            '斑疹伤寒': {
                'incubation_period': 12,  # 7-14天
                'r0': 2.5,  # 2-3
                'seasonal_pattern': {
                    1: 1.2, 2: 1.2, 3: 1.1, 4: 1.0,
                    5: 0.9, 6: 0.8, 7: 0.8, 8: 0.9,
                    9: 1.0, 10: 1.1, 11: 1.1, 12: 1.2
                },
                'transmission_type': 'vector'
            },
            '新生儿破伤风': {
                'incubation_period': 7,  # 3-21天
                'r0': 0,  # 不存在人传人
                'seasonal_pattern': {
                    1: 1.0, 2: 1.0, 3: 1.0, 4: 1.0,
                    5: 1.0, 6: 1.0, 7: 1.0, 8: 1.0,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'wound'
            },
            '梅毒': {
                'incubation_period': 21,  # 10-90天
                'r0': 2.0,  # 1.5-2.5
                'seasonal_pattern': {
                    1: 1.0, 2: 1.0, 3: 1.0, 4: 1.0,
                    5: 1.0, 6: 1.0, 7: 1.0, 8: 1.0,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'contact'
            },
            '流行性乙型脑炎': {
                'incubation_period': 7,  # 4-14天
                'r0': 0.5,  # 蚊虫传播
                'seasonal_pattern': {
                    1: 0.7, 2: 0.7, 3: 0.8, 4: 1.0,
                    5: 1.2, 6: 1.4, 7: 1.5, 8: 1.4,
                    9: 1.2, 10: 1.0, 11: 0.8, 12: 0.7
                },
                'transmission_type': 'vector'
            },
            '流行性出血热': {
                'incubation_period': 14,  # 7-21天
                'r0': 0.3,  # 主要通过啮齿类传播
                'seasonal_pattern': {
                    1: 1.1, 2: 1.0, 3: 0.9, 4: 0.8,
                    5: 0.7, 6: 0.8, 7: 0.9, 8: 1.0,
                    9: 1.2, 10: 1.3, 11: 1.2, 12: 1.1
                },
                'transmission_type': 'zoonotic'
            },
            '流行性脑脊髓炎': {
                'incubation_period': 4,  # 3-7天
                'r0': 1.5,  # 1.3-1.7
                'seasonal_pattern': {
                    1: 1.3, 2: 1.4, 3: 1.3, 4: 1.1,
                    5: 0.9, 6: 0.7, 7: 0.7, 8: 0.8,
                    9: 0.9, 10: 1.0, 11: 1.1, 12: 1.2
                },
                'transmission_type': 'respiratory'
            },
            '淋病': {
                'incubation_period': 3,  # 2-7天
                'r0': 1.5,  # 1.0-2.0
                'seasonal_pattern': {
                    1: 1.0, 2: 1.0, 3: 1.0, 4: 1.0,
                    5: 1.0, 6: 1.0, 7: 1.0, 8: 1.0,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'contact'
            },
            '炭疽': {
                'incubation_period': 5,  # 1-7天
                'r0': 0.1,  # 极少人传人
                'seasonal_pattern': {
                    1: 0.9, 2: 0.9, 3: 1.0, 4: 1.1,
                    5: 1.2, 6: 1.2, 7: 1.1, 8: 1.1,
                    9: 1.0, 10: 0.9, 11: 0.9, 12: 0.9
                },
                'transmission_type': 'zoonotic'
            },
            '猩红热': {
                'incubation_period': 3,  # 1-7天
                'r0': 2.0,  # 1.5-2.5
                'seasonal_pattern': {
                    1: 1.3, 2: 1.4, 3: 1.3, 4: 1.1,
                    5: 0.9, 6: 0.7, 7: 0.7, 8: 0.8,
                    9: 0.9, 10: 1.0, 11: 1.1, 12: 1.2
                },
                'transmission_type': 'respiratory'
            },
            '猴痘': {
                'incubation_period': 10,  # 5-21天
                'r0': 1.2,  # 1.0-1.4
                'seasonal_pattern': {
                    1: 1.0, 2: 1.0, 3: 1.0, 4: 1.0,
                    5: 1.0, 6: 1.0, 7: 1.0, 8: 1.0,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'contact'
            },
            '白喉': {
                'incubation_period': 3,  # 2-5天
                'r0': 2.5,  # 2.0-3.0
                'seasonal_pattern': {
                    1: 1.2, 2: 1.2, 3: 1.1, 4: 1.0,
                    5: 0.9, 6: 0.8, 7: 0.8, 8: 0.9,
                    9: 1.0, 10: 1.1, 11: 1.1, 12: 1.2
                },
                'transmission_type': 'respiratory'
            },
            '百日咳': {
                'incubation_period': 10,  # 7-14天
                'r0': 15.0,  # 12-17
                'seasonal_pattern': {
                    1: 1.1, 2: 1.1, 3: 1.1, 4: 1.0,
                    5: 0.9, 6: 0.9, 7: 0.9, 8: 0.9,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.1
                },
                'transmission_type': 'respiratory'
            },
            '脊髓灰质炎': {
                'incubation_period': 7,  # 3-21天
                'r0': 5.0,  # 4-6
                'seasonal_pattern': {
                    1: 0.8, 2: 0.8, 3: 0.9, 4: 1.0,
                    5: 1.2, 6: 1.3, 7: 1.3, 8: 1.2,
                    9: 1.1, 10: 1.0, 11: 0.9, 12: 0.8
                },
                'transmission_type': 'contact'
            },
            '血吸虫病': {
                'incubation_period': 30,  # 14-84天
                'r0': 0.3,  # 通过钉螺传播
                'seasonal_pattern': {
                    1: 0.8, 2: 0.8, 3: 0.9, 4: 1.1,
                    5: 1.2, 6: 1.3, 7: 1.3, 8: 1.2,
                    9: 1.1, 10: 1.0, 11: 0.9, 12: 0.8
                },
                'transmission_type': 'vector'
            },
            '钩端螺旋体病': {
                'incubation_period': 10,  # 2-30天
                'r0': 0.3,  # 主要通过污染水源传播
                'seasonal_pattern': {
                    1: 0.7, 2: 0.7, 3: 0.8, 4: 1.0,
                    5: 1.2, 6: 1.4, 7: 1.5, 8: 1.4,
                    9: 1.2, 10: 1.0, 11: 0.8, 12: 0.7
                },
                'transmission_type': 'water_food'
            },
            '风疹': {
                'incubation_period': 17,  # 14-21天
                'r0': 6.0,  # 5-7
                'seasonal_pattern': {
                    1: 1.2, 2: 1.3, 3: 1.2, 4: 1.1,
                    5: 1.0, 6: 0.8, 7: 0.7, 8: 0.8,
                    9: 0.9, 10: 1.0, 11: 1.1, 12: 1.2
                },
                'transmission_type': 'respiratory'
            },
            '麻风病': {
                'incubation_period': 1825,  # 2-20年
                'r0': 0.3,  # 传染性较低
                'seasonal_pattern': {
                    1: 1.0, 2: 1.0, 3: 1.0, 4: 1.0,
                    5: 1.0, 6: 1.0, 7: 1.0, 8: 1.0,
                    9: 1.0, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'contact'
            },
            '黑热病': {
                'incubation_period': 90,  # 10天-数月
                'r0': 0.2,  # 通过白蛉传播
                'seasonal_pattern': {
                    1: 0.8, 2: 0.8, 3: 0.9, 4: 1.0,
                    5: 1.2, 6: 1.3, 7: 1.3, 8: 1.2,
                    9: 1.1, 10: 1.0, 11: 0.9, 12: 0.8
                },
                'transmission_type': 'vector'
            },
            '鼠疫': {
                'incubation_period': 4,  # 1-7天
                'r0': 3.0,  # 2.5-3.5
                'seasonal_pattern': {
                    1: 1.0, 2: 0.9, 3: 0.9, 4: 1.0,
                    5: 1.1, 6: 1.2, 7: 1.2, 8: 1.1,
                    9: 1.1, 10: 1.0, 11: 1.0, 12: 1.0
                },
                'transmission_type': 'zoonotic'
            }
        }

    def get_disease_factors(self, disease_name, month):
        """获取疾病特定月份的影响因子"""
        disease = self.disease_characteristics.get(disease_name.lower(), {})
        if not disease:
            return 1.0
            
        # 获取季节性模式
        seasonal_factor = disease.get('seasonal_pattern', {}).get(month, 1.0)
        # 获取假期权重
        holiday_factor = self.holiday_weights.get(month, 1.0)
        
        # 综合影响因子
        return seasonal_factor * holiday_factor

    def get_data_from_db(self, start_date=None, end_date=None, disease=None):
        """从数据库获取中国疾病数据"""
        try:
            connection = sqlite3.connect(SQLITE_DB_PATH)
            
            query = """
                SELECT time, disaster, growth, deaths
                FROM t_disaster_china 
                WHERE growth IS NOT NULL
                AND sub_disaster = ''
            """
            
            start_date_str = start_date.strftime('%Y-%m-%d') if isinstance(start_date, datetime) else start_date
            end_date_str = end_date.strftime('%Y-%m-%d') if isinstance(end_date, datetime) else end_date
            
            if disease:
                query += " AND disaster = ?"
                params = (disease, start_date_str, end_date_str)
            else:
                params = (start_date_str, end_date_str)
            
            query += " AND time BETWEEN ? AND ? ORDER BY time"
            
            # 使用pandas直接从SQLite读取数据
            df = pd.read_sql_query(query, connection, params=params)
            
            # 使用mixed模式转换日期列，可以自动处理多种格式
            df['time'] = pd.to_datetime(df['time'], format='mixed')
            
            # 计算治愈人数 = 感染人数 - 死亡人数
            df['cures'] = df['growth'] - df['deaths']
            
            connection.close()
            return df
            
        except Exception as e:
            print(f"Error in get_data_from_db: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def prepare_data(self, df):
        """准备预测所需的数据"""
        try:
            if len(df) < 12:
                print("Insufficient data for prediction")
                return None
            
            # 添加月份信息
            df['month'] = df['time'].dt.month
            
            # 添加疾病特征和季节性因子
            disease_name = df['disaster'].iloc[0].lower()
            df['factor'] = df['month'].apply(lambda x: self.get_disease_factors(disease_name, x))
            
            # 设置时间索引但保持原始日期
            monthly_data = df.set_index('time')
            monthly_data.index = pd.DatetimeIndex(monthly_data.index).to_period('M').to_timestamp('M')
            monthly_data.index.freq = 'M'
            
            # 保持历史数据为整数
            monthly_data['growth'] = monthly_data['growth'].astype(int)
            monthly_data['deaths'] = monthly_data['deaths'].astype(int)
            monthly_data['cures'] = monthly_data['cures'].astype(int)
            
            return monthly_data[['growth', 'deaths', 'cures', 'factor']]
            
        except Exception as e:
            print(f"Error in prepare_data: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def fit_sarima_model(self, data, prediction_months=6, disease_name=None):
        """拟合SARIMA模型并进行预测"""
        try:
            # 检查数据类型并进行适当的处理
            if isinstance(data, pd.DataFrame):
                # 如果是DataFrame，分离数据和因子
                prediction_data = data.copy()
                if 'factor' in data.columns:
                    factors = data['factor']
                    data = data.drop('factor', axis=1)
            else:
                # 如果是Series，直接使用
                prediction_data = data.copy()

            # 计算历史数据的统计特征
            historical_mean = data.mean()
            historical_std = data.std()
            historical_min = data.min()
            last_value = data.iloc[-1]
            
            # 设置SARIMA模型参数
            model = SARIMAX(
                data,
                order=(1, 1, 1),
                seasonal_order=(1, 1, 1, 12),
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            
            results = model.fit(disp=False)
            
            # 进行预测
            forecast = results.get_forecast(steps=prediction_months)
            mean_forecast = forecast.predicted_mean
            confidence_intervals = forecast.conf_int(alpha=0.05)
            
            # 应用疾病特征和季节性因子到预测值
            if disease_name:
                for i in range(len(mean_forecast)):
                    # 计算预测月份
                    current_month = (pd.to_datetime(mean_forecast.index[i]).month)
                    # 获取该月份的影响因子
                    factor = self.get_disease_factors(disease_name, current_month)
                    
                    # 调整预测值和置信区间
                    mean_forecast.iloc[i] *= factor
                    confidence_intervals.iloc[i] *= factor
            
            # 处理预测值，保持自然变化
            for i in range(len(mean_forecast)):
                current_pred = mean_forecast.iloc[i]
                
                if current_pred < 0:
                    # 基于历史数据和趋势调整
                    time_factor = np.exp(-i * 0.1)
                    base_value = last_value * 0.7 + historical_mean * 0.3
                    
                    # 加随机波动
                    random_factor = np.random.normal(1, 0.1)
                    
                    # 计算新的预测值
                    adjusted_value = max(
                        base_value * time_factor * random_factor,
                        historical_min * 0.5
                    )
                    
                    mean_forecast.iloc[i] = adjusted_value
            
            # 调整置信区间
            for i in range(len(mean_forecast)):
                pred_value = mean_forecast.iloc[i]
                time_uncertainty = 1 + i * 0.1
                
                lower_ratio = 0.8 - i * 0.05
                lower_bound = pred_value * max(0.5, lower_ratio)
                
                upper_ratio = 1.2 + i * 0.1
                upper_bound = pred_value * min(2.0, upper_ratio)
                
                # 添加随机波动
                lower_bound *= np.random.uniform(0.95, 1.05)
                upper_bound *= np.random.uniform(0.95, 1.05)
                
                confidence_intervals.iloc[i] = [lower_bound, upper_bound]
            
            return {
                'mean': mean_forecast,
                'lower': confidence_intervals.iloc[:, 0],
                'upper': confidence_intervals.iloc[:, 1]
            }
            
        except Exception as e:
            print(f"Error in fit_sarima_model: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def get_prediction(self, disease, start_date, end_date, prediction_months=6):
        """获取疾病预测结果"""
        try:
            # 获取数据
            df = self.get_data_from_db(start_date, end_date, disease)
            monthly_data = self.prepare_data(df)
            
            if monthly_data is None:
                return None, "insufficient_data"
            
            # 对三个指标分别进行预测
            predictions = {}
            for column in ['growth', 'deaths', 'cures']:
                pred = self.fit_sarima_model(monthly_data[column], prediction_months)
                predictions[column] = pred
            
            # 准备返回结果
            last_date = monthly_data.index[-1]
            future_dates = pd.date_range(
                start=last_date + pd.DateOffset(months=1),
                periods=prediction_months,
                freq='M'
            )
            
            # 转换数据为列表格式
            historical_dates = [d.strftime('%Y-%m-%d') for d in monthly_data.index]
            future_dates = [d.strftime('%Y-%m-%d') for d in future_dates]
            
            # 准备历史数据
            historical_data = {
                'growth': monthly_data['growth'].tolist(),
                'deaths': monthly_data['deaths'].tolist(),
                'cures': monthly_data['cures'].tolist()
            }
            
            # 准备预测数据
            prediction_data = {
                'growth': {
                    'mean': predictions['growth']['mean'].tolist(),
                    'lower': predictions['growth']['lower'].tolist(),
                    'upper': predictions['growth']['upper'].tolist()
                },
                'deaths': {
                    'mean': predictions['deaths']['mean'].tolist(),
                    'lower': predictions['deaths']['lower'].tolist(),
                    'upper': predictions['deaths']['upper'].tolist()
                },
                'cures': {
                    'mean': predictions['cures']['mean'].tolist(),
                    'lower': predictions['cures']['lower'].tolist(),
                    'upper': predictions['cures']['upper'].tolist()
                }
            }
            
            prediction_results = {
                'historical_dates': historical_dates,
                'dates': future_dates,
                'historical_data': historical_data,
                'predictions': prediction_data
            }
            
            return prediction_results, "success"
            
        except Exception as e:
            print(f"Error in get_prediction: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None, "prediction_failed"

def handler():
    try:
        disease = request.form.get('disease')
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        prediction_period = int(request.form.get('prediction_period', 6))
        
        if not all([disease, start_date, end_date]):
            return jsonify({
                "error": "Missing required parameters",
                "status": "invalid_params"
            }), 400
            
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({
                "error": "Invalid date format",
                "status": "invalid_date"
            }), 400
            
        # 初始化预测模型
        predictor = SARIMAPredictionChina()
        prediction_results, status = predictor.get_prediction(
            disease, 
            start_date, 
            end_date, 
            prediction_period
        )
        
        if status != "success" or not prediction_results:
            error_messages = {
                "insufficient_data": "历史数据不足，预测需要至少12个月的历史数据记录。",
                "prediction_failed": "预测过程中发生错误，请稍后重试。",
            }
            return jsonify({
                "error": error_messages.get(status, "预测失败"),
                "status": status
            }), 400
        
        # 验证返回的数据结构
        required_fields = ['dates', 'historical_dates', 'historical_data', 'predictions']
        if not all(field in prediction_results for field in required_fields):
            print("Missing required fields in prediction results")
            return jsonify({
                "error": "预测结果数据结构不完整",
                "status": "invalid_data"
            }), 500
            
        # 验证数据类型
        if not isinstance(prediction_results['dates'], list) or \
           not isinstance(prediction_results['historical_dates'], list) or \
           not isinstance(prediction_results['historical_data'], dict) or \
           not isinstance(prediction_results['predictions'], dict):
            print("Invalid data types in prediction results")
            return jsonify({
                "error": "预测结果数据类型错误",
                "status": "invalid_data"
            }), 500
        
        # 确保返回的数据结构正确
        response_data = {
            'historical_dates': prediction_results['historical_dates'],
            'dates': prediction_results['dates'],
            'historical_data': prediction_results['historical_data'],
            'predictions': prediction_results['predictions']
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error in predict_disease_china: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": str(e), "status": "server_error"}), 500 