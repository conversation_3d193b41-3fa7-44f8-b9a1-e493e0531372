"""
数据库配置文件
包含所有与数据库相关的配置选项
"""
import os
import sys

# 获取项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 数据目录和数据库文件配置
DATA_DIR = os.path.join(PROJECT_ROOT, 'data')
DB_NAME = 'disease_prediction.db'
SQLITE_DB_PATH = os.path.join(DATA_DIR, DB_NAME)

# 确保数据目录存在
try:
    os.makedirs(os.path.dirname(SQLITE_DB_PATH), exist_ok=True)
except OSError as e:
    print(f"无法创建数据目录: {str(e)}")
    sys.exit(1)

class Config:
    """Flask-SQLAlchemy配置类"""
    # 基本配置
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{SQLITE_DB_PATH}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 性能配置
    SQLALCHEMY_POOL_SIZE = 10
    SQLALCHEMY_POOL_TIMEOUT = 30
    SQLALCHEMY_POOL_RECYCLE = 3600
    
    # 调试配置（生产环境应设置为False）
    SQLALCHEMY_ECHO = False
    
    @classmethod
    def get_database_url(cls):
        """获取数据库URL"""
        return cls.SQLALCHEMY_DATABASE_URI