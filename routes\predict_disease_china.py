from flask import jsonify, request
import traceback
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import sqlite3
from db.db_config import SQLITE_DB_PATH
from scipy.integrate import odeint
from routes.sarima_prediction_china import SARIMAPredictionChina


class SEIRModel:
    def __init__(self, beta, sigma, gamma, N):
        """
        初始化SEIR模型
        beta: 传染率
        sigma: 潜伏期转化率 (1/潜伏期天数)
        gamma: 恢复率 (1/感染持续天数)
        N: 总人口
        """
        self.beta = beta
        self.sigma = sigma
        self.gamma = gamma
        self.N = N

    def model(self, y, t):
        """SEIR模型的微分方程组"""
        S, E, I, R = y
        dSdt = -self.beta * S * I / self.N
        dEdt = self.beta * S * I / self.N - self.sigma * E
        dIdt = self.sigma * E - self.gamma * I
        dRdt = self.gamma * I
        return dSdt, dEdt, dIdt, dRdt

    def predict(self, S0, E0, I0, R0, t):
        """预测未来趋势"""
        y0 = S0, E0, I0, R0
        ret = odeint(self.model, y0, t)
        return ret.T


class DiseasePredictionChina:
    def __init__(self):
        # 加载疾病参数配置
        self.disease_params = {
            "covid-19": {"incubation": 5, "duration": 14, "beta": 0.3},
            "influenza": {"incubation": 2, "duration": 7, "beta": 0.5},
            "default": {"incubation": 4, "duration": 10, "beta": 0.4},
        }

        # 人口数据
        self.population_data = {"china": 1400000000, "default": 1000000}

    def get_model_parameters(self, disease, disease_data):
        """获取模型参数"""
        disease_key = disease.lower()
        params = self.disease_params.get(disease_key, self.disease_params["default"])

        # 从数据估计传染率beta（如果有足够数据）
        if len(disease_data) > 14:
            growth_rate = disease_data["growth"].mean()
            if growth_rate > 0:
                params["beta"] = min(max(growth_rate / 100, 0.1), 0.9)

        return params

    def get_data_from_db(self, start_date=None, end_date=None):
        """从数据库获取中国疾病数据"""
        try:
            connection = sqlite3.connect(SQLITE_DB_PATH)

            query = """
                SELECT time, disaster, growth, deaths
                FROM t_disaster_china
                WHERE growth IS NOT NULL
                AND sub_disaster = ''
            """

            start_date_str = (
                start_date.strftime("%Y-%m-%d")
                if isinstance(start_date, datetime)
                else start_date
            )
            end_date_str = (
                end_date.strftime("%Y-%m-%d")
                if isinstance(end_date, datetime)
                else end_date
            )

            query += " AND time BETWEEN ? AND ? ORDER BY time"

            # 使用pandas直接从SQLite读取数据
            df = pd.read_sql_query(
                query, connection, params=(start_date_str, end_date_str)
            )

            # 使用mixed模式转换日期列，可以自动处理多种格式
            df["time"] = pd.to_datetime(df["time"], format="mixed")

            # 计算治愈人数 = 感染人数 - 死亡人数
            df["cures"] = df["growth"] - df["deaths"]

            connection.close()
            return df

        except Exception as e:
            print(f"Error in get_data_from_db: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def prepare_data(self, df, disease):
        """准备预测所需的数据"""
        try:
            disease_data = df[df["disaster"] == disease].copy()

            if len(disease_data) < 10:
                print(f"Insufficient data for disease {disease}")
                return None

            disease_data = disease_data.sort_values("time")
            disease_data["total_cases"] = disease_data["growth"].cumsum()
            disease_data["total_deaths"] = disease_data["deaths"].cumsum()
            disease_data["total_cures"] = disease_data["cures"].cumsum()

            return disease_data

        except Exception as e:
            print(f"Error in prepare_data: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def get_prediction_for_china(
        self, disease, start_date, end_date, prediction_months=6
    ):
        """获取中国特定疾病的预测"""
        try:
            # 获取数据
            df = self.get_data_from_db(start_date, end_date)
            disease_data = self.prepare_data(df, disease)

            if disease_data is None:
                return None, "insufficient_data"

            # 获取模型参数
            params = self.get_model_parameters(disease, disease_data)

            # 使用默认人口数据
            N = 1400000000  # 约14亿人口

            # 初始化SEIR模型
            model = SEIRModel(
                beta=params["beta"],
                sigma=1 / params["incubation"],
                gamma=1 / params["duration"],
                N=N,
            )

            # 设置初始值
            latest_data = disease_data.iloc[-1]
            I0 = max(
                latest_data["total_cases"]
                - latest_data["total_cures"]
                - latest_data["total_deaths"],
                0,
            )
            R0 = latest_data["total_cures"] + latest_data["total_deaths"]
            E0 = I0 * 0.5  # 估计潜伏人数
            S0 = N - E0 - I0 - R0

            # 预测时间点（按月预测）
            prediction_days = prediction_months * 30
            t = np.linspace(0, prediction_days, prediction_days)

            # 进行预测
            S, E, I, R = model.predict(S0, E0, I0, R0, t)

            # 计算每日新增
            daily_new_cases = np.diff(I + R, prepend=I[0] + R[0])

            # 准备返回结果
            last_date = disease_data["time"].iloc[-1]
            prediction_results = {
                "dates": [
                    (last_date + timedelta(days=int(x))).strftime("%Y-%m-%d") for x in t
                ],
                "predicted_growth": daily_new_cases.tolist(),
                "lower_bound": (daily_new_cases * 0.8).tolist(),
                "upper_bound": (daily_new_cases * 1.2).tolist(),
                "susceptible": S.tolist(),
                "exposed": E.tolist(),
                "infected": I.tolist(),
                "recovered": R.tolist(),
            }

            return prediction_results, "success"

        except Exception as e:
            print(f"Error in get_prediction_for_china: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None, "prediction_failed"

    def get_combined_prediction(
        self, disease, start_date, end_date, prediction_months=6
    ):
        """获取SEIR和SARIMA模型的组合预测结果"""
        try:
            # 获取SEIR模型预测
            seir_prediction, seir_status = self.get_prediction_for_china(
                disease, start_date, end_date, prediction_months
            )
            if seir_status != "success":
                return None, seir_status

            # 获取SARIMA模型预测
            sarima_predictor = SARIMAPredictionChina()
            sarima_prediction, sarima_status = sarima_predictor.get_prediction(
                disease, start_date, end_date, prediction_months
            )
            if sarima_status != "success":
                return None, sarima_status

            # 设置模型权重（可以根据实际情况调整）
            seir_weight = 0.4  # SEIR模型权重
            sarima_weight = 0.6  # SARIMA模型权重

            # 组合预测结果
            combined_prediction = {
                "dates": seir_prediction["dates"],
                "predictions": [],
                "lower_bound": [],
                "upper_bound": [],
                "seir": {
                    "susceptible": seir_prediction["susceptible"],
                    "exposed": seir_prediction["exposed"],
                    "infected": seir_prediction["infected"],
                    "recovered": seir_prediction["recovered"],
                },
                "sarima": sarima_prediction["predictions"]["growth"],
            }

            # 由于SEIR按天预测，SARIMA按月预测，需要对齐数据
            # 使用SARIMA的月度数据，将SEIR的日度数据聚合为月度
            sarima_growth_mean = sarima_prediction["predictions"]["growth"]["mean"]
            sarima_growth_lower = sarima_prediction["predictions"]["growth"]["lower"]
            sarima_growth_upper = sarima_prediction["predictions"]["growth"]["upper"]

            # 将SEIR的日度数据按月聚合
            seir_daily_growth = seir_prediction["predicted_growth"]
            seir_daily_lower = seir_prediction["lower_bound"]
            seir_daily_upper = seir_prediction["upper_bound"]

            # 按30天一个月聚合SEIR数据
            seir_monthly_growth = []
            seir_monthly_lower = []
            seir_monthly_upper = []

            for month_idx in range(prediction_months):
                start_day = month_idx * 30
                end_day = min((month_idx + 1) * 30, len(seir_daily_growth))

                if start_day < len(seir_daily_growth):
                    # 计算该月的平均值
                    month_growth = sum(seir_daily_growth[start_day:end_day]) / (
                        end_day - start_day
                    )
                    month_lower = sum(seir_daily_lower[start_day:end_day]) / (
                        end_day - start_day
                    )
                    month_upper = sum(seir_daily_upper[start_day:end_day]) / (
                        end_day - start_day
                    )

                    seir_monthly_growth.append(month_growth)
                    seir_monthly_lower.append(month_lower)
                    seir_monthly_upper.append(month_upper)

            # 组合月度预测值
            for i in range(min(len(seir_monthly_growth), len(sarima_growth_mean))):
                seir_value = seir_monthly_growth[i]
                sarima_value = sarima_growth_mean[i]

                # 计算加权平均值
                combined_value = seir_weight * seir_value + sarima_weight * sarima_value
                combined_prediction["predictions"].append(combined_value)

                # 计算组合的置信区间
                seir_lower = seir_monthly_lower[i]
                seir_upper = seir_monthly_upper[i]
                sarima_lower = sarima_growth_lower[i]
                sarima_upper = sarima_growth_upper[i]

                combined_lower = seir_weight * seir_lower + sarima_weight * sarima_lower
                combined_upper = seir_weight * seir_upper + sarima_weight * sarima_upper

                combined_prediction["lower_bound"].append(combined_lower)
                combined_prediction["upper_bound"].append(combined_upper)

            # 使用SARIMA的日期（月度）
            combined_prediction["dates"] = sarima_prediction["dates"]

            return combined_prediction, "success"

        except Exception as e:
            print(f"Error in get_combined_prediction: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None, "prediction_failed"


def handler():
    try:
        disease = request.form.get("disease")
        start_date = request.form.get("start_date")
        end_date = request.form.get("end_date")
        prediction_period = int(request.form.get("prediction_period", 6))

        if not all([disease, start_date, end_date]):
            return jsonify(
                {"error": "Missing required parameters", "status": "invalid_params"}
            ), 400

        # 验证日期格式
        try:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
        except ValueError:
            return jsonify(
                {"error": "Invalid date format", "status": "invalid_date"}
            ), 400

        # 初始化预测模型
        predictor = DiseasePredictionChina()
        prediction_results, status = predictor.get_combined_prediction(
            disease, start_date, end_date, prediction_period
        )

        if status != "success":
            return jsonify({"error": "Prediction failed", "status": status}), 500

        # 获取历史数据用于前端显示
        historical_df = predictor.get_data_from_db(start_date, end_date)
        disease_data = predictor.prepare_data(historical_df, disease)

        if disease_data is not None:
            historical_dates = disease_data["time"].dt.strftime("%Y-%m-%d").tolist()
            historical_growth = disease_data["growth"].tolist()
            historical_deaths = disease_data["deaths"].tolist()
            historical_cures = disease_data["cures"].tolist()
        else:
            historical_dates = []
            historical_growth = []
            historical_deaths = []
            historical_cures = []

        # 转换为前端期望的SARIMA格式
        response_data = {
            "historical_dates": historical_dates,
            "dates": prediction_results["dates"],
            "historical_data": {
                "growth": historical_growth,
                "deaths": historical_deaths,
                "cures": historical_cures,
            },
            "predictions": {
                "growth": {
                    "mean": prediction_results["predictions"],
                    "lower": prediction_results["lower_bound"],
                    "upper": prediction_results["upper_bound"],
                },
                "deaths": {
                    "mean": [0]
                    * len(
                        prediction_results["predictions"]
                    ),  # 组合预测暂时不包含死亡预测
                    "lower": [0] * len(prediction_results["predictions"]),
                    "upper": [0] * len(prediction_results["predictions"]),
                },
                "cures": {
                    "mean": [0]
                    * len(
                        prediction_results["predictions"]
                    ),  # 组合预测暂时不包含治愈预测
                    "lower": [0] * len(prediction_results["predictions"]),
                    "upper": [0] * len(prediction_results["predictions"]),
                },
            },
            "seir": prediction_results["seir"],
            "sarima": prediction_results["sarima"],
        }

        return jsonify(response_data)

    except Exception as e:
        print(f"Error in predict_disease_china: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": str(e), "status": "server_error"}), 500
