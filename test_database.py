#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd
from db.db_config import SQLITE_DB_PATH
import os

def test_database():
    """测试数据库连接和数据"""
    print("=== 数据库测试 ===")
    
    # 检查数据库文件是否存在
    if not os.path.exists(SQLITE_DB_PATH):
        print(f"数据库文件不存在: {SQLITE_DB_PATH}")
        return False
    
    try:
        conn = sqlite3.connect(SQLITE_DB_PATH)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"数据库中的表: {tables}")
        
        # 检查t_disaster_china表的数据
        cursor.execute("SELECT COUNT(*) FROM t_disaster_china")
        total_count = cursor.fetchone()[0]
        print(f"t_disaster_china表总记录数: {total_count}")
        
        if total_count > 0:
            # 检查主疾病数据（sub_disaster为空的记录）
            cursor.execute("SELECT COUNT(*) FROM t_disaster_china WHERE sub_disaster = '' OR sub_disaster IS NULL")
            main_disease_count = cursor.fetchone()[0]
            print(f"主疾病记录数: {main_disease_count}")
            
            # 查看疾病类型
            cursor.execute("SELECT DISTINCT disaster FROM t_disaster_china WHERE sub_disaster = '' OR sub_disaster IS NULL LIMIT 10")
            diseases = cursor.fetchall()
            print(f"疾病类型样本: {[d[0] for d in diseases]}")
            
            # 查看时间范围
            cursor.execute("SELECT MIN(time), MAX(time) FROM t_disaster_china")
            time_range = cursor.fetchone()
            print(f"数据时间范围: {time_range[0]} 到 {time_range[1]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"数据库测试失败: {e}")
        return False

def test_prediction_function():
    """测试预测功能"""
    print("\n=== 预测功能测试 ===")
    
    try:
        from routes.predict_disease_china import DiseasePredictionChina
        from datetime import datetime
        
        predictor = DiseasePredictionChina()
        
        # 测试数据获取
        start_date = datetime(2022, 1, 1)
        end_date = datetime(2023, 12, 31)
        
        print("测试数据获取...")
        df = predictor.get_data_from_db(start_date, end_date)
        print(f"获取到数据行数: {len(df)}")
        
        if len(df) > 0:
            print(f"数据列: {df.columns.tolist()}")
            print(f"疾病类型: {df['disaster'].unique()[:5]}")  # 显示前5个疾病类型
            
            # 测试数据准备
            if len(df['disaster'].unique()) > 0:
                test_disease = df['disaster'].unique()[0]
                print(f"测试疾病: {test_disease}")
                
                disease_data = predictor.prepare_data(df, test_disease)
                if disease_data is not None:
                    print(f"准备的疾病数据行数: {len(disease_data)}")
                    return True
                else:
                    print("数据准备失败")
                    return False
        else:
            print("没有获取到数据")
            return False
            
    except Exception as e:
        print(f"预测功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    db_ok = test_database()
    if db_ok:
        test_prediction_function()
    else:
        print("数据库测试失败，跳过预测功能测试")
