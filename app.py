from flask import Flask, request, jsonify, render_template, redirect, url_for, Response
from models.models import db, Disease  # 只保留Disease模型
from db.db_config import Config  # 导入数据库配置
import os, json
from datetime import datetime
import pandas as pd
from routes.cdc_news_china import handler as cdc_news_china_handler
from routes.disease_data_china import handler as parse_disease_data_china_handler
from routes.import_disease_data_china import (
    handler as import_disease_data_china_handler,
)
from routes.get_diseases_china import handler as get_diseases_china_handler
from routes.predict_disease_china import handler as predict_disease_china_handler
from routes.sarima_prediction_china import handler as sarima_prediction_china_handler
from utils.report_generator import generate_report

app = Flask(__name__)
app.config.from_object(Config)  # 使用配置对象
db.init_app(app)  # 初始化应用


# 主页
@app.route("/")
def index():
    return render_template("index.html")


@app.route("/disease_prediction_china")
def disease_prediction_china():
    return render_template("disease_prediction_china.html")


@app.route("/get_diseases_china")
def get_diseases_china():
    return get_diseases_china_handler()


@app.route("/predict_disease_china", methods=["POST"])
def predict_disease_china():
    return predict_disease_china_handler()


@app.errorhandler(404)
def page_not_found(e):
    return render_template("404.html"), 404


@app.after_request
def add_security_headers(response):
    # 添加安全性和缓存控制头
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["Cache-Control"] = "no-store, must-revalidate"  # 对于API响应用缓存
    response.headers["Server"] = "Flask"
    response.headers["Secure-Cookie"] = "true"  # 添加安全cookie指令

    # 对于静态文件设置更长的缓存时间
    if request.path.startswith("/static/"):
        response.headers["Cache-Control"] = "public, max-age=31536000"  # 1年缓存

    # 删除不推荐使用的头
    response.headers.pop("Pragma", None)

    return response


@app.route("/cdc_news_china")
def cdc_news_china():
    return render_template("cdc_news_china.html")


@app.route("/api/cdc_news_china")
def get_cdc_news_china():
    return cdc_news_china_handler()


@app.route("/api/parse_disease_data_china")
def parse_disease_data_china():
    return parse_disease_data_china_handler()


@app.route("/api/import_disease_data_china", methods=["POST"])
def import_disease_data_china():
    return import_disease_data_china_handler()


# LLM配置路由
@app.route("/llm_config")
def llm_config():
    return render_template("llm_config.html")


@app.route("/api/llm_config/load")
def load_llm_config():
    config_file = "config/llm_config.json"
    default_prompt = """你是一位专业的疾病预测分析专家。请根据提供的传染病预测数据生成一份专业的分析报告。报告应包含：

1. 疫情趋势分析：
   - 分析感染人数、死亡人数、治愈人数的变化趋势
   - 识别关键拐点和重要时间节点
   - 预测未来发展趋势

2. 风险评估：
   - 评估当前传播风险等级
   - 分析影响疾病传播的关键因素
   - 识别高风险人群和地区

3. 防控建议：
   - 针对性的防控措施建议
   - 医疗资源调配建议
   - 公共卫生干预策略

请确保报告语言专业、客观，并提供具体的数据支持。"""

    try:
        if os.path.exists(config_file):
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)
                if not config.get("prompt"):  # 如果配置文件中没有prompt或prompt为空
                    config["prompt"] = default_prompt
                return jsonify(config)
        else:
            return jsonify(
                {
                    "apiEndpoint": "https://api.suanli.cn/v1",
                    "apiKey": "sk-dHBTGOwm7q1FIyVbfyz1HcCqSvFIGMSDI0nX24yQSdWtfb9Z",
                    "model": "free:Qwen3-30B-A3B",
                    "prompt": default_prompt,
                }
            )
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/api/llm_config/save", methods=["POST"])
def save_llm_config():
    try:
        config_dir = "config"
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)

        config_file = os.path.join(config_dir, "llm_config.json")
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(request.json, f, ensure_ascii=False, indent=2)
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/api/generate_report", methods=["POST"])
def generate_disease_report():
    try:
        prediction_data = request.json
        if not prediction_data:
            # 对于流式传输，错误也应以流的方式发送，或者在这里返回一个非流式错误响应
            return jsonify({"success": False, "error": "预测数据不能为空"}), 400

        print("Received prediction data for streaming report:", prediction_data)

        # generate_report 现在是一个生成器
        stream = generate_report(prediction_data)

        # 返回流式响应
        # 使用 text/event-stream 可以在前端使用 EventSource API
        return Response(stream, mimetype="text/event-stream")

    except Exception as e:
        print("Error setting up report stream:", str(e))  # 捕获设置流时的初始错误
        # 初始设置错误，返回非流式错误
        return jsonify({"success": False, "error": f"设置报告流失败: {str(e)}"}), 500


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=83, debug=True)
