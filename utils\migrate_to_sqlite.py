"""
数据库迁移工具 - 将MySQL数据迁移到SQLite
"""
import os
import sys
import pymysql
import sqlite3
from datetime import datetime
import pandas as pd

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.db_config import (
    SQLITE_DB_PATH, 
    MYSQL_USERNAME, 
    MYSQL_PASSWORD, 
    MYSQL_HOST, 
    MYSQL_PORT, 
    MYSQL_DATABASE
)

def create_sqlite_tables():
    """创建SQLite数据库表结构"""
    conn = sqlite3.connect(SQLITE_DB_PATH)
    cursor = conn.cursor()
    
    # 创建news_diseases表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS news_diseases (
        data_value TEXT PRIMARY KEY,
        name TEXT NOT NULL
    )
    ''')
    
    # 创建t_disaster_china表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS t_disaster_china (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        time TIMESTAMP,
        disaster TEXT,
        disaster_type TEXT,
        sub_disaster TEXT,
        cities TEXT,
        source_url TEXT,
        growth INTEGER NOT NULL,
        deaths INTEGER,
        cures INTEGER,
        sub_growth INTEGER NOT NULL,
        sub_deaths INTEGER,
        sub_cures INTEGER,
        update_time TIMESTAMP
    )
    ''')
    
    conn.commit()
    conn.close()
    print("SQLite表结构创建完成")

def migrate_data():
    """从MySQL迁移数据到SQLite"""
    try:
        # 连接MySQL数据库
        mysql_conn = pymysql.connect(
            host=MYSQL_HOST,
            user=MYSQL_USERNAME,
            password=MYSQL_PASSWORD,
            database=MYSQL_DATABASE,
            port=int(MYSQL_PORT)
        )
        
        # 连接SQLite数据库
        sqlite_conn = sqlite3.connect(SQLITE_DB_PATH)
        
        # 迁移news_diseases表
        print("开始迁移news_diseases表...")
        mysql_cursor = mysql_conn.cursor()
        mysql_cursor.execute("SELECT data_value, name FROM news_diseases")
        diseases_data = mysql_cursor.fetchall()
        
        if diseases_data:
            df_diseases = pd.DataFrame(diseases_data, columns=['data_value', 'name'])
            df_diseases.to_sql('news_diseases', sqlite_conn, if_exists='replace', index=False)
            print(f"成功迁移 {len(df_diseases)} 条news_diseases记录")
        else:
            print("news_diseases表中没有数据")
        
        # 迁移t_disaster_china表
        print("开始迁移t_disaster_china表...")
        mysql_cursor.execute("""
            SELECT id, time, disaster, disaster_type, sub_disaster, cities, 
                   source_url, growth, deaths, cures, sub_growth, sub_deaths, 
                   sub_cures, update_time 
            FROM t_disaster_china
        """)
        disaster_data = mysql_cursor.fetchall()
        
        if disaster_data:
            columns = ['id', 'time', 'disaster', 'disaster_type', 'sub_disaster', 
                      'cities', 'source_url', 'growth', 'deaths', 'cures', 
                      'sub_growth', 'sub_deaths', 'sub_cures', 'update_time']
            df_disaster = pd.DataFrame(disaster_data, columns=columns)
            
            # 处理日期时间格式
            for col in ['time', 'update_time']:
                df_disaster[col] = pd.to_datetime(df_disaster[col])
            
            df_disaster.to_sql('t_disaster_china', sqlite_conn, if_exists='replace', index=False)
            print(f"成功迁移 {len(df_disaster)} 条t_disaster_china记录")
        else:
            print("t_disaster_china表中没有数据")
        
        # 关闭连接
        mysql_cursor.close()
        mysql_conn.close()
        sqlite_conn.close()
        
        print("数据迁移完成")
        return True
    except Exception as e:
        print(f"迁移过程中出错: {str(e)}")
        return False

def main():
    print("开始数据库迁移: MySQL -> SQLite")
    
    # 创建SQLite表结构
    create_sqlite_tables()
    
    # 迁移数据
    success = migrate_data()
    
    if success:
        print("数据库迁移成功完成!")
    else:
        print("数据库迁移失败，请检查错误信息")

if __name__ == "__main__":
    main()
