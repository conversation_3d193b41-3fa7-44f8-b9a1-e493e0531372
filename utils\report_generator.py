import json
import os
from openai import OpenAI
from datetime import datetime
import base64

def generate_report(prediction_data):
    """生成预测报告 (流式)"""
    try:
        # 加载LLM配置
        config = load_llm_config()
        
        # 准备输入数据
        input_data = prepare_prediction_data(prediction_data)
        
        # 调用AI流式生成报告内容
        for chunk in call_llm(input_data, config):
            yield chunk # 直接将LLM的输出块返回

    except Exception as e:
        print(f"生成报告失败: {e}")
        # 在流中报告错误
        yield json.dumps({"error": str(e)})

def load_llm_config():
    """加载LLM配置"""
    config_file = 'config/llm_config.json'
    if not os.path.exists(config_file):
        raise Exception("配置文件不存在")
        
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
        
    required_fields = ['apiEndpoint', 'apiKey', 'model', 'prompt']
    for field in required_fields:
        if not config.get(field):
            raise Exception(f"配置文件缺少必要字段: {field}")
            
    return config

def prepare_prediction_data(prediction_data):
    """准备预测数据的文本描述"""
    try:
        # 提取实际数据和预测数据
        actual_data = prediction_data.get('actual_data', [])
        predicted_data = prediction_data.get('predicted_data', [])
        
        # 计算一些基本统计信息
        latest_actual = actual_data[-1] if actual_data else None
        latest_predict = predicted_data[-1] if predicted_data else None
        
        # 构建输入文本
        input_text = f"""
传染病预测数据分析：

1. 最新实际数据（{latest_actual['date'] if latest_actual else 'N/A'}）：
- 感染人数：{latest_actual.get('infected', 'N/A')}
- 死亡人数：{latest_actual.get('dead', 'N/A')}
- 治愈人数：{latest_actual.get('cured', 'N/A')}

2. 最新预测数据（{latest_predict['date'] if latest_predict else 'N/A'}）：
- 预测感染人数：{latest_predict.get('infected', 'N/A')}
- 预测死亡人数：{latest_predict.get('dead', 'N/A')}
- 预测治愈人数：{latest_predict.get('cured', 'N/A')}

3. 预测时间范围：
- 开始日期：{prediction_data.get('start_date', 'N/A')}
- 结束日期：{prediction_data.get('end_date', 'N/A')}

4. 疾病名称：{prediction_data.get('disease_name', 'N/A')}
"""
        return input_text
    except Exception as e:
        raise Exception(f"准备预测数据失败: {e}")

def call_llm(input_text, config):
    """调用LLM流式生成报告"""
    try:
        client = OpenAI(
            base_url=config['apiEndpoint'],
            api_key=config['apiKey']
        )
        
        stream = client.chat.completions.create(
            model=config['model'],
            messages=[
                {"role": "system", "content": config['prompt']},
                {"role": "user", "content": input_text}
            ],
            stream=True, # 启用流式输出
        )

        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content
    except Exception as e:
        # 在流中报告错误
        error_message = json.dumps({"error": f"调用AI模型失败: {e}"})
        yield error_message # 确保错误信息作为流的一部分返回
        print(f"调用AI模型失败: {e}") # 仍然在服务器端打印日志

def generate_txt_report(report_content, prediction_data):
    """生成TXT格式的报告 (保留此函数，但流式接口不直接使用)"""
    try:
        # 构建报告内容
        report_text = f"""传染病预测分析报告
{'='*50}

生成时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
疾病名称：{prediction_data.get('disease_name', '未知')}
预测时间范围：{prediction_data.get('start_date', 'N/A')} 至 {prediction_data.get('end_date', 'N/A')}

{'='*50}

{report_content}

{'='*50}
报告生成系统由人工智能辅助生成
"""
        # 转换为base64
        report_bytes = report_text.encode('utf-8')
        base64_content = base64.b64encode(report_bytes).decode('utf-8')
        
        return base64_content
    except Exception as e:
        raise Exception(f"生成TXT报告失败: {e}") 