from flask import jsonify
from models.models import db
from sqlalchemy import text
import traceback

def handler():
    try:
        # 从t_disaster_china表中获取不重复的疾病列表
        # SQLite不支持MySQL的CONVERT和COLLATE语法，使用简单排序
        query = text("""
            SELECT DISTINCT disaster as value, disaster as name
            FROM t_disaster_china
            WHERE disaster IS NOT NULL
            ORDER BY disaster
        """)
        
        result = db.session.execute(query)
        diseases = [{"value": row.value, "name": row.name} for row in result]
        
        return jsonify({"diseases": diseases})
        
    except Exception as e:
        print(f"Error in get_diseases_china: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": "获取疾病列表失败", "details": str(e)}), 500