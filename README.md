# 疾病预测分析系统 - 少年开发者项目

## 项目介绍

这是一个用Python开发的疾病预测分析系统，可以分析和预测中国的疾病传播情况。

## 这个项目能做什么？

这个系统能够：
1. 收集和分析疾病数据
2. 使用先进的SARIMA时间序列模型预测疾病趋势
3. 使用SEIR传染病模型模拟疾病传播
4. 通过人工智能生成专业的疾病预测报告
5. 以图表和数据可视化方式展示结果

所有这些功能都与大赛的"数据驱动开发思维"主题高度契合，完美体现了"数据工厂"的核心理念！
建议选择【主题：人工智能赋能数据多元表达中（1）数据"会说话"】

## 项目文件结构（简明解释）

```
疾病预测分析系统/
├── app.py                  # 应用程序主入口，整个程序的"大脑"
├── setup_env.py            # 自动设置环境的工具，帮你安装所有需要的程序
├── start.bat               # Windows系统下快速启动应用的批处理文件，自动处理环境设置和启动
├── requirements.txt        # 项目需要安装的所有软件包列表
├── models/                 # 数据库：数据模型定义，决定如何存储数据
├── routes/                 # API路由，处理不同网页请求的功能
├── db/                     # 数据库配置和初始化
├── utils/                  # 工具函数，包括AI报告生成器
├── templates/              # 网页模板，定义了网站的页面结构
├── static/                 # 静态资源，如图片、CSS样式和JavaScript脚本
├── config/                 # 大模型配置文件
└── data/                   # 本地数据库文件
```

## 重要文件详解

### 1. app.py
这是整个应用的主入口，就像整个系统的"大脑"。它控制网站的所有页面和功能，决定当用户点击某个按钮时会发生什么。

### 2. models/models.py
这个文件定义了如何在数据库中存储疾病数据。想象它是一个特殊的表格设计，告诉计算机如何整理所有疾病信息。

### 3. routes/目录中的文件
这些文件处理不同类型的网页请求：
- `cdc_news_china.py`: 从中国疾控中心获取最新疫情新闻
- `disease_data_china.py`: 处理中国疾病数据的收集和分析
- `import_disease_data_china.py`: 导入疾病数据的功能
- `predict_disease_china.py`: 实现疾病预测功能
- `sarima_prediction_china.py`: 包含先进的SARIMA时间序列预测模型

### 4. utils/report_generator.py
这是一个智能AI报告生成器，能够分析疾病数据并自动创建专业的分析报告。它使用了先进的大型语言模型(LLM)技术。
使用通用的openai大模型接口，调用大模型来生成分析报告。

### 5. templates/目录
这里存放了网站的所有页面设计：
- `index.html`: 主页
- `disease_prediction_china.html`: 疾病预测页面
- `cdc_news_china.html`: 疾控中心新闻页面
- `llm_config.html`: AI配置页面

## 如何运行这个项目

### 方法一：使用一键启动脚本（推荐）

1. 确保你的电脑已安装Python（推荐Python 3.11.9版本）
2. 双击 `start.bat` 文件
3. 脚本会自动：
   - 检查是否存在虚拟环境
   - 如果不存在，自动创建虚拟环境并安装所需依赖
   - 激活虚拟环境
   - 启动应用程序
4. 打开浏览器访问: http://localhost:83

### 方法二：手动设置环境

1. 确保你的电脑已安装Python（推荐Python 3.11.9版本）
2. 打开命令提示符（在Windows搜索栏中输入"cmd"并点击）
3. 进入项目目录（使用`cd 项目路径`命令）
4. 运行以下命令：
   ```
   python setup_env.py
   ```
5. 脚本会自动为你:
   - 创建一个名为"venv"的虚拟环境（这是一个独立的空间，不会影响电脑上的其他程序）
   - 安装所有需要的软件包
   - 告诉你下一步该怎么做

6. 激活虚拟环境：
   ```
   venv\Scripts\activate
   ```

7. 启动应用：
   ```
   python app.py
   ```

8. 打开浏览器访问: http://localhost:83

## 自定义参赛者信息

在首页显示的参赛者信息可以通过修改 `templates/index.html` 文件来自定义：

1. 打开 `templates/index.html` 文件
2. 找到以下代码段：
```html
<div class="contestant-info">
    <h3>第五届长三角青少年人工智能奥林匹克挑战赛参赛作品</h3>
    <div class="contestant-details">
        <div class="info-item">
            <i class="fas fa-school"></i>
            <span class="school">XX中学</span>
        </div>
        <div class="info-item">
            <i class="fas fa-users"></i>
            <span class="class">X(X)班</span>
        </div>
        <div class="info-item">
            <i class="fas fa-user"></i>
            <span class="name">参赛者姓名</span>
        </div>
    </div>
</div>
```

3. 修改以下内容：
   - 将 `XX中学` 改为你的学校名称
   - 将 `X(X)班` 改为你的班级
   - 将 `参赛者姓名` 改为你的姓名

4. 如果你想隐藏参赛者信息区域，可以修改文件开头的 JavaScript 代码：
```javascript
// 设置为true显示参赛者信息，false隐藏参赛者信息
const showContestantInfo = true;
```
将 `true` 改为 `false` 即可隐藏参赛者信息。

## 页面与路由对应关系

### 1. 首页
- **HTML模板**: `templates/index.html`
- **路由**: `@app.route('/')`
- **处理函数**: `index()` (在 `app.py` 中)

### 2. 数据获取、入库
- **HTML模板**: `templates/cdc_news_china.html`
- **路由**: `@app.route('/cdc_news_china')`
- **API路由**: `@app.route('/api/cdc_news_china')`
- **处理函数**: `cdc_news_china_handler()` (在 `routes/cdc_news_china.py` 中)
- **功能**: 从中国疾控中心获取最新疫情新闻

- **API路由**: `@app.route('/api/parse_disease_data_china')`
- **处理函数**: `parse_disease_data_china_handler()` (在 `routes/disease_data_china.py` 中)
- **功能**: 解析疾病数据

- **API路由**: `@app.route('/api/import_disease_data_china')` [POST]
- **处理函数**: `import_disease_data_china_handler()` (在 `routes/import_disease_data_china.py` 中)
- **功能**: 将解析后的疾病数据导入数据库

### 3. 预测
- **HTML模板**: `templates/disease_prediction_china.html`
- **路由**: `@app.route('/disease_prediction_china')`
- **功能**: 显示疾病预测页面

- **路由**: `@app.route('/get_diseases_china')`
- **处理函数**: `get_diseases_china_handler()` (在 `routes/get_diseases_china.py` 中)
- **功能**: 获取数据库中所有疾病列表

- **路由**: `@app.route('/predict_disease_china')` [POST]
- **处理函数**: `sarima_prediction_china_handler()` (在 `routes/sarima_prediction_china.py` 中)
- **功能**: 使用SARIMA模型进行疾病预测

- **API路由**: `@app.route('/api/generate_report')` [POST]
- **处理函数**: `generate_report()` (在 `utils/report_generator.py` 中)
- **功能**: 使用大型语言模型生成疾病预测分析报告

### 4. 大模型配置
- **HTML模板**: `templates/llm_config.html`
- **路由**: `@app.route('/llm_config')`
- **处理函数**: `llm_config()` (在 `app.py` 中)
- **功能**: 配置AI大模型接口参数

- **API路由**: `@app.route('/api/llm_config/load')`
- **处理函数**: `load_llm_config()` (在 `app.py` 中)
- **功能**: 加载当前的大模型配置信息

- **API路由**: `@app.route('/api/llm_config/save')` [POST]
- **处理函数**: `save_llm_config()` (在 `app.py` 中)
- **功能**: 保存更新后的大模型配置信息

## 这个项目与比赛的关系

这个项目非常适合"数智创变者"赛道，因为它：

1. **数据融合与创意呈现**：融合了疾病感染数据、死亡数据、治愈数据等多种数据类型，并通过交互式图表创新地展示。

2. **数据洞察与趋势应用**：通过SARIMA和SEIR模型分析疾病数据中的规律，预测未来趋势，提供决策支持。

3. **数据建模与技术实践**：使用时间序列预测和传染病模型进行数据建模，将AI技术应用于实际问题解决。

4. **价值创新**：通过AI生成的专业报告，帮助医疗机构和公共卫生部门做出更好的防疫决策。

## 预测模块详解

本系统的核心功能之一是疾病预测，主要通过两种模型实现：SEIR模型和SARIMA模型。用户可以在前端选择历史数据范围、要预测的疾病种类以及预测未来多少个月。

### 1. 数据处理与获取

- **`routes/disease_data_china.py`**: 此脚本负责从外部（如中国疾控中心发布的HTML网页）抓取原始的疾病数据。 
    - `parse_disease_data(html_content, date, source_url)`: 使用`BeautifulSoup`库解析HTML表格，提取各省份或全国的每日新增病例数、死亡数等。它能够识别主疾病和亚型疾病（通过文本缩进判断）。
    - `handler()`: 一个Flask路由，接收目标URL和日期作为参数，调用解析函数，并以JSON格式返回结构化的疾病数据。

- **数据入库**: 解析后的数据通常会通过另一个接口（如 `routes/import_disease_data_china.py` 中定义的）存入SQLite数据库（`data/disease_prediction.db`）的 `t_disaster_china` 表中，供后续预测模型使用。

### 2. SEIR 传染病模型

- **文件**: `routes/predict_disease_china.py`
- **模型**: SEIR (Susceptible, Exposed, Infected, Recovered) 是一种经典的传染病动力学隔间模型，用于模拟传染病在人群中的传播过程。
    - **`SEIRModel` 类**:
        - `__init__(self, beta, sigma, gamma, N)`: 初始化模型参数。
            - `beta`: 传染率（易感者接触感染者后被感染的概率）。
            - `sigma`: 潜伏期转化率（潜伏者转化为感染者的速率，即1/平均潜伏期天数）。
            - `gamma`: 恢复率（感染者恢复或死亡的速率，即1/平均感染持续天数）。
            - `N`: 总人口数。
        - `model(self, y, t)`: 定义SEIR模型的微分方程组，描述S, E, I, R四个状态之间随时间`t`的变化。
        - `predict(self, S0, E0, I0, R0, t)`: 使用`scipy.integrate.odeint`求解微分方程组，预测在时间点`t`上S, E, I, R各仓室的人数。
    - **`DiseasePredictionChina` 类**:
        - `__init__(self)`: 初始化时会加载一些常见疾病（如COVID-19, 流感）的默认参数（潜伏期、持续期、基础传染率beta）。
        - `get_model_parameters(self, disease, disease_data)`: 根据选择的疾病和历史数据，获取或调整模型参数。如果历史数据记录超过14条，`beta`值会根据近期数据的平均增长率 (`disease_data['growth'].mean()`) 进行动态估算，并通过 `min(max(growth_rate / 100, 0.1), 0.9)` 约束在0.1到0.9之间；否则使用预设值。
        - `get_data_from_db(self, start_date=None, end_date=None)`: 从SQLite数据库中提取指定日期范围内的历史病例数据（新增、死亡、治愈）。如果数据少于10条记录，则认为数据不足。
        - `prepare_data(self, df, disease)`: 针对特定疾病准备模型输入数据，如计算累计病例数。
        - `get_prediction_for_china(self, disease, start_date, end_date, prediction_months=6)`: 核心预测函数。
            1. 获取并准备指定疾病的历史数据。
            2. 确定SEIR模型参数（`sigma` 来自 `1/潜伏期`, `gamma` 来自 `1/感染持续期`, `N` - 中国人口固定为14亿）。
            3. 根据最新历史数据设置初始条件 `S0` (易感者), `E0` (潜伏者), `I0` (感染者), `R0` (康复/移除者)。其中，`E0` 根据 `I0 * 0.5` 估算。
            4. 生成预测时间序列（未来`prediction_months`个月，按天计算）。
            5. 调用`SEIRModel.predict()`进行预测。
            6. 计算每日新增病例数，通过计算 `I+R` (感染者+康复/移除者总数) 的日增量得到，即 `np.diff(I + R, prepend=I[0] + R[0])`。并返回预测结果（包括S, E, I, R各仓室人数和每日新增）。

### 3. SARIMA 时间序列模型

- **文件**: `routes/sarima_prediction_china.py`
- **模型**: SARIMA (Seasonal Autoregressive Integrated Moving Average) 是一种用于分析和预测具有季节性模式的时间序列数据的统计模型。
    - **`SARIMAPredictionChina` 类**:
        - `__init__(self)`: 初始化时定义了：
            - `holiday_weights`: 月度假期权重，用于调整节假日期间可能的传播率变化（如春节、国庆节期间权重较高）。
            - `disease_characteristics`: 疾病特征字典，包含每种疾病的：
                - `incubation_period`: 潜伏期（天）
                - `r0`: 基本再生数
                - `seasonal_pattern`: 月度季节性模式（1-12月的系数）
                - `transmission_type`: 传播类型（如呼吸道、接触传播等）
        - `get_disease_factors(self, disease_name, month)`: 获取特定疾病在特定月份的影响因子，结合疾病季节性模式和假期权重。
        - `get_data_from_db(self, start_date=None, end_date=None, disease=None)`: 从数据库获取历史数据。
        - `prepare_data(self, df)`: 准备预测数据，包括：
            - 添加月份信息
            - 应用疾病特征和季节性因子
            - 设置时间索引
        - `fit_sarima_model(self, data, prediction_months=6, disease_name=None)`: 拟合SARIMA模型并进行预测：
            - 使用SARIMAX模型，参数为(1,1,1)和季节性参数(1,1,1,12)
            - 应用疾病特征和季节性因子调整预测值
            - 处理预测值，确保自然变化
            - 计算置信区间
        - `get_prediction(self, disease, start_date, end_date, prediction_months=6)`: 获取预测结果，包括：
            - 获取并准备数据
            - 对新增病例、死亡、治愈三个指标分别进行预测
            - 返回预测结果和置信区间

### 4. 组合预测

- **文件**: `routes/predict_disease_china.py`
- **功能**: 将SEIR和SARIMA模型的预测结果进行组合，以获得更准确的预测。
    - **`get_combined_prediction` 方法**:
        - 分别获取SEIR和SARIMA模型的预测结果
        - 使用权重组合（SEIR: 0.4, SARIMA: 0.6）
        - 合并置信区间
        - 返回组合后的预测结果，包括：
            - 预测日期
            - 预测值
            - 置信区间
            - 原始SEIR和SARIMA预测结果（用于参考）

### 4. 前端展示

- **文件**: `templates/disease_prediction_china.html`
- **技术栈**: HTML, JavaScript, JQuery, Arco Design (React UI库), ECharts。
- **功能**:
    - 提供用户界面让用户选择疾病类型、历史数据范围和预测周期。
    - 点击"生成预测"按钮后，通过AJAX（异步JavaScript和XML）向后端 `/predict_disease_china` (或 `/sarima_predict_china_route`，根据前端实际调用) 发送请求。
    - 接收后端返回的JSON格式预测数据。
    - 使用ECharts将历史数据和预测数据（包括SEIR模型的S,E,I,R曲线或SARIMA模型的预测值及置信区间）动态渲染成交互式图表。
    - 提供帮助抽屉（Help Drawer）解释模型参数和图表含义。
    - 支持生成和下载包含图表和分析的预测报告（可能通过调用 `utils/report_generator.py` 中的AI报告生成功能）。

### 总结

系统提供了两种互补的预测方法：
- **SEIR模型**：基于传染病传播机理，更侧重于理解疾病传播的动态过程和不同人群（易感、潜伏、感染、康复）的比例变化。适合于有较明确流行病学参数（如潜伏期、传染期）的疾病。
- **SARIMA模型**：一种纯粹基于历史数据的时间序列统计模型，擅长捕捉数据中的趋势、季节性和自相关性，而无需深入了解疾病本身的具体传播机制。它通过整合假期和季节性因素作为外部特征来增强预测的准确性。在有足够长且具有明显季节性特征的历史数据时表现较好。

用户可以根据具体需求和数据情况，参考两种模型的预测结果进行综合判断。
